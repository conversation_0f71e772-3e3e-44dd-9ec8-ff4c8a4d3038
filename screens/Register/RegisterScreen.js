import React, { useState } from "react";
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Keyboard } from "react-native";
import { CustomButton, CustomInput } from "components/CustomAction";
import CustomCheckbox from "components/CustomAction/CustomCheckbox";
import { Colors } from "constants/theme/colors";
import { useNavigation } from '@react-navigation/native';
import authService from 'services/authService';
import CustomAlert from 'components/CustomAction/CustomAlert';
import CustomLoader from 'components/CustomAction/CustomLoader';

export const RegisterScreen = () => {
    const navigator = useNavigation();
    const [localErrors, setLocalErrors] = useState({});
    const [showAlert, setShowAlert] = useState(false);
    const [loading, setLoading] = useState(false);

    const [localValues, setLocalValues] = useState({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        acceptTerms: false
    });

    const validatePassword = (password) => {
        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W)[A-Za-z\d\W]{8,}$/;
        return regex.test(password);
    };

    const validateEmail = (email) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    };

    const validate = () => {
        const newErrors = {};

        if (!localValues.name) {
            newErrors.name = "Username is required";
        }
        else if (localValues.name.trim().length < 3) {
            newErrors.name = "Username must be at least 3 characters long";
        }
        else if (!localValues.email) {
            newErrors.email = "Email is required";
        }
        else if (!validateEmail(localValues.email)) {
            newErrors.email = "Please enter a valid email";
        }
        else if (!localValues.password) {
            newErrors.password = "Password is required";
        }
        else if (!validatePassword(localValues.password)) {
            newErrors.password =
                "Password must be at least 8 characters long, include an uppercase letter, a lowercase letter, a number, and a special character (@$!%*?&)";
        }
        else if (localValues.password !== localValues.confirmPassword) {
            newErrors.confirmPassword = "Passwords do not match";
        }
        else if (!localValues.acceptTerms) {
            newErrors.terms = "You must accept the terms";
        }

        setLocalErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const handleBlur = (field) => {
        setLocalErrors((prevErrors) => {
            const newErrors = { ...prevErrors };
            if (field === "name" && localValues.name.trim()) delete newErrors.name;
            if (field === "email" && validateEmail(localValues.email)) delete newErrors.email;
            if (field === "password" && validatePassword(localValues.password)) delete newErrors.password;
            if (field === "confirmPassword" && localValues.password === localValues.confirmPassword) delete newErrors.confirmPassword;
            if (field === "acceptTerms" && localValues.acceptTerms) delete newErrors.terms;

            return newErrors;
        });
    };

    const handleRegister = async () => {
        setLoading(true);
        if (validate()) {
            try {
                const userData = {
                    name: localValues.name,
                    email: localValues.email,
                    password: localValues.password,
                    confirmPassword: localValues.confirmPassword,
                    acceptTerms: localValues.acceptTerms,
                    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                };

                const res = await authService.createUser(userData);
                setShowAlert(true);

                setLoading(false);
                return res;
            } catch (error) {
                setLoading(false);
                setLocalErrors({
                    register: error?.message || "Error registering user"
                });
            }
        }
        setLoading(false);
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
        >

            <CustomAlert
                visible={showAlert}
                title="Registered Successfully"
                message={`Verification link has been sent to your ${localValues.email} Please check your inbox.`}
                buttons={[
                    { text: "OK", onPress: () => navigator.navigate('Login'), style: "allowButton" }
                ]}
                onClose={() => setShowAlert(false)}
            />

            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                onStartShouldSetResponder={() => true}
                showsVerticalScrollIndicator={false}
                onTouchStsart={() => Keyboard.dismiss()}
                keyboardShouldPersistTaps="handled"
            >
                {loading && <CustomLoader />}
                <Text style={styles.text}>Register</Text>
                {localErrors.register && <Text style={[styles.error]}>{localErrors.register}</Text>}
                <CustomInput
                    value={localValues.name}
                    onChangeText={(value) => setLocalValues({ ...localValues, name: value })}
                    placeholder="Username"
                    onBlur={() => handleBlur("name")}
                    editable={!loading}
                    clearValidationError={() => setLocalErrors((prev) => ({ ...prev, name: "" }))}
                />
                {localErrors.name && <Text style={styles.error}>{localErrors.name}</Text>}
                <CustomInput
                    value={localValues.email}
                    onChangeText={(value) => setLocalValues({ ...localValues, email: value })}
                    placeholder="Email"
                    onBlur={() => handleBlur("email")}
                    editable={!loading}
                    clearValidationError={() => setLocalErrors((prev) => ({ ...prev, email: "" }))}
                />
                {localErrors.email && <Text style={styles.error}>{localErrors.email}</Text>}

                <CustomInput
                    value={localValues.password}
                    onChangeText={(value) => setLocalValues({ ...localValues, password: value })}
                    placeholder="Create Password"
                    secureTextEntry
                    onBlur={() => handleBlur("password")}
                    editable={!loading}
                    clearValidationError={() => setLocalErrors((prev) => ({ ...prev, password: "" }))}
                />
                {localErrors.password && <Text style={styles.error}>{localErrors.password}</Text>}

                <CustomInput
                    value={localValues.confirmPassword}
                    onChangeText={(value) => setLocalValues({ ...localValues, confirmPassword: value })}
                    placeholder="Confirm Password"
                    secureTextEntry
                    onBlur={() => handleBlur("confirmPassword")}
                    editable={!loading}
                    clearValidationError={() => setLocalErrors((prev) => ({ ...prev, confirmPassword: "" }))}
                />
                {localErrors.confirmPassword && <Text style={styles.error}>{localErrors.confirmPassword}</Text>}

                <View style={styles.termsContainer}>
                    <CustomCheckbox
                        title="I agree to Appetec's Terms & Conditions & Privacy Policy."
                        checked={localValues.acceptTerms}
                        updateStatus={(status) => setLocalValues({ ...localValues, acceptTerms: status })}
                        onBlur={() => handleBlur("acceptTerms")}
                        clearValidationError={() => setLocalErrors((prev) => ({ ...prev, terms: "" }))}
                    />
                </View>
                {localErrors.terms && <Text style={styles.error}>{localErrors.terms}</Text>}

                <View style={styles.buttonContainer}>
                    <CustomButton
                        title="Back"
                        onPress={() => navigator.goBack()}
                        textColor={Colors.primaryPurple}
                        backgroundColor={Colors.lightGreen}
                        textStyle={{ fontFamily: "Exo_700Bold" }}
                    />
                    <CustomButton
                        title="Register"
                        // onPress={() => setLoading(true)}
                        onPress={handleRegister}
                        textColor={Colors.primaryPurple}
                        textStyle={{ fontFamily: "Exo_700Bold" }}
                        backgroundColor={Colors.lightGreen}
                    />
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        padding: 20,
        backgroundColor: Colors.primaryGreen,
    },
    loader: {
        position: 'absolute',
        marginTop: 50,
        left: 'auto',
        zIndex: 12,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        // marginVertical: 'auto',
        // marginHorizontal: 'auto'
    },
    scrollContainer: {
        // marginTop: 100,
        marginVertical: 100,
        paddingBottom: 80,  // Add some padding at the bottom for space
    },
    error: {
        color: 'red',
        fontSize: 12,
        marginHorizontal: 10,
        fontFamily: 'Exo_400Regular',
    },
    termsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginTop: 'auto',
        // marginVertical: 5,
    },
    termsText: {
        color: Colors.white,
        fontFamily: 'Exo_400Regular',
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    text: {
        fontSize: 32,
        marginBottom: 38,
        fontFamily: 'Exo_700Bold',
        textAlign: 'center',
        color: Colors.white,
    },
});
