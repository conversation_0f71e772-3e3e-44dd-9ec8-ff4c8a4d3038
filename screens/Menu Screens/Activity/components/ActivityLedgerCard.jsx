import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { Colors } from 'constants/theme/colors'
import { ThemeFonts } from 'constants/theme/fonts'
import useActivityStore from 'store/activityStore'
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader'

const ActivityLedgerCard = () => {
    const { isLoadingActivityLedger, activityLedgerData } = useActivityStore(state => state);

    if (isLoadingActivityLedger) return <SkeletonItem width={'100%'} height={86} borderRadius={25} isLoading={isLoadingActivityLedger} style={{ marginTop: 16 }} />

    if (!activityLedgerData) return;

    return (
        <View
            style={{
                flexDirection: "row",
                alignItems: "center",
                backgroundColor: Colors.primaryGreen,
                padding: 16,
                borderRadius: 25,
                marginTop: 16,
                // left: -20
            }}
        >
            <View style={styles.activityMetric}>
                <Text style={styles.metricValue}>{Number(activityLedgerData.totalBurnedCalories).toFixed(0)}</Text>
                <Text style={styles.metricLabel}>Active Energy</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.activityMetric}>
                <Text style={styles.metricValue}>{activityLedgerData.totalSteps}</Text>
                <Text style={styles.metricLabel}>Steps</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.activityMetric}>
                <Text style={styles.metricValue}>{activityLedgerData.heartRate?.min ? `${activityLedgerData.heartRate?.min}/${activityLedgerData.heartRate?.max}` : '-/-'}</Text>
                <Text style={styles.metricLabel}>Heart Rate</Text>
            </View>
        </View>
    )
}

export default ActivityLedgerCard

const styles = StyleSheet.create({
    activityMetric: {
        alignItems: "center",
        flex: 1,
    },
    metricValue: {
        fontSize: 24,
        fontFamily: ThemeFonts.Lexend_700,
        color: Colors.white,
    },
    metricLabel: {
        fontSize: 13,
        fontFamily: ThemeFonts.Exo_400,
        color: Colors.white,
        marginTop: 4,
    },
    verticalDivider: {
        width: 1,
        height: 50,
        backgroundColor: Colors.white,
        marginHorizontal: 8,
    },
})