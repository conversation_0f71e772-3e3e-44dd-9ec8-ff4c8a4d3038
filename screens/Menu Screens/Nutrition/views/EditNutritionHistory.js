import { StyleSheet, Text, View, ScrollView, TouchableWithoutFeedback, TouchableOpacity } from 'react-native';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { CustomLoader, CustomSelect } from 'components/CustomAction';
import NutritionReminderCard from '../components/NutritionOverviewCard';
import apiClient from 'services/axiosInstance';
import { Ionicons } from '@expo/vector-icons';
// Import date formatting utilities
import { format, subDays } from 'date-fns';
import useNutritionMealRecordStore, { AVAILABLE_MEAL_TYPES } from 'store/nutritionMealRecordStore';
import CustomCalender from 'components/CustomPickers/CustomCalender';
import { SkeletonList, SkeletonItem } from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';


const EditNutritionHistory = () => {
    const scrollViewRef = useRef();
    const navigation = useNavigation();

    const { isLoadingMealRecords, historyRecords, mealRecordsError } = useNutritionMealRecordStore(state => state);
    const { getHistoryMealRecords } = useNutritionMealRecordStore(state => state);

    const [selected, setSelected] = useState(new Date().toISOString().split('T')[0]);

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
    const [expandMealsList, setExpandMealsList] = useState(false);

    useEffect(() => {
        getHistoryMealRecords(selected);
    }, [selected]);

    return (
        <AppLayout bgColor={Colors.primaryGreen} paddingHorizontal={0}>
            <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef} contentContainerStyle={{ flexGrow: 1 }}>
                <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
                    <View style={styles.container}>
                        <CustomCalender heading="Nutrition" totalDays={7} currentMonth={selected} setCurrentMonth={(value) => setSelected(value)} currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)} />
                        <View style={styles.section}>
                            <View style={styles.header}>
                                <Text style={[styles.heading, { fontSize: 32, color: Colors.black }]}>Nutrition</Text>
                                {
                                    isLoadingMealRecords ? (
                                        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginHorizontal: 20, marginBottom: 10, marginTop: 8 }}>
                                            <SkeletonItem
                                                width="25%"
                                                height={26}
                                                style={{ marginBottom: 8 }}
                                                isLoading={true}
                                            />
                                            <SkeletonItem
                                                width="16%"
                                                height={16}
                                                style={{ marginBottom: 8 }}
                                                isLoading={true}
                                            />
                                        </View >
                                    ) : (
                                        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginHorizontal: 20, marginBottom: 10 }}>

                                            <Text style={styles.totalCaloriesText}>
                                                {historyRecords.reduce((total, mealRecord) => total + mealRecord.meals.reduce((sum, meal) => sum + meal.calories, 0), 0)} kcal
                                            </Text>
                                            <Text style={styles.lastLogTimeText}>
                                                {new Date(selected).toLocaleDateString('en-US', { day: 'numeric', month: 'short' })}
                                            </Text>
                                        </View >
                                    )
                                }
                            </View>
                            {/* Display meal types with Show More/Less functionality */}
                            <View style={styles.mealsList}>
                                {/* Show a subset or all available meal types based on expandMealsList state */}
                                <SkeletonList items={2} height={150} gap={24} borderRadius={25} isLoading={isLoadingMealRecords} style={{ marginBottom: 4 }}>
                                    {(expandMealsList ? historyRecords : historyRecords.slice(0, 2)).map((mealRecord, index) => {
                                        return (
                                            <NutritionReminderCard
                                                key={index}
                                                record={mealRecord}
                                                selectedDate={selected}
                                                disabled={isLoadingMealRecords}
                                                onPress={() => {
                                                    if (mealData && mealData.id) {
                                                        navigation.navigate("Edit Nutrition", { mealRecordId: mealData.id, mealType: mealType });
                                                    } else {
                                                        console.log("Navigating with meal type:", selected);
                                                        navigation.navigate("Edit Nutrition", {
                                                            mealType: mealType,
                                                            selectedDate: selected
                                                        });
                                                    }
                                                }}
                                                isLoading={isLoadingMealRecords}
                                            />
                                        );
                                    })}
                                </SkeletonList>

                                {/* Show More/Less button */}
                                {AVAILABLE_MEAL_TYPES.length > 2 && (
                                    <TouchableOpacity
                                        activeOpacity={.5}
                                        onPress={() => setExpandMealsList(prev => !prev)}
                                        style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 30, marginTop: 10 }}
                                        disabled={isLoadingMealRecords}
                                    >
                                        <Text style={styles.showMoreText}>
                                            {expandMealsList ? 'Show Less' : 'Show More'}
                                        </Text>
                                        <Ionicons
                                            name={expandMealsList ? "chevron-up" : "chevron-down"}
                                            size={16}
                                            color={Colors.primaryPurple}
                                            style={{ marginLeft: 5 }}
                                        />
                                    </TouchableOpacity>
                                )}
                            </View>
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </AppLayout>
    );
};

export default EditNutritionHistory;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    heading: {
        fontSize: 35,
        color: Colors.white,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700,
        marginHorizontal: 20,
        // marginBottom: 10,
    },
    section: {
        flex: 1,
        backgroundColor: Colors.white,
        borderTopLeftRadius: 30,
        borderTopRightRadius: 30,
        padding: 20,
        paddingTop: 30,
        zIndex: 9998,
        marginTop: 16,
    },
    header: {
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 24,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    mealsList: {
        gap: 16,
        marginBottom: 70,
    },
    showMoreText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.primaryPurple,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
        marginTop: 30,
    },
    emptyStateText: {
        fontSize: 16,
        color: Colors.black,
        textAlign: 'center',
        fontFamily: ThemeFonts.Exo_500,
        marginBottom: 20,
    },
    addMealBtn: {
        width: 'auto',
        paddingHorizontal: 40,
        height: 36
    },
    totalCaloriesText: {
        fontSize: 24,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
    },
    lastLogTimeText: {
        fontSize: 14,
        fontFamily: ThemeFonts.Exo_500,
        color: Colors.black,
        textDecorationLine: "underline"
    },
});