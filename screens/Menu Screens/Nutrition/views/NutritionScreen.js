import { FlatList, RefreshControl, ScrollView, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { ThemeFonts } from 'constants/theme/fonts';
import NutritionReminderCard from '../components/NutritionOverviewCard';
import { useNavigation } from '@react-navigation/native';
import { Colors } from 'constants/theme/colors';
import { CustomAlert, CustomButton } from 'components/CustomAction';
import RecipeList from '../../../../components/CustomHorizontalLists/RecipeList';

import { recipesService } from 'services/recipesService';
import FormattedTime from 'components/CustomText/FormattedTime';
import { Ionicons } from '@expo/vector-icons';
import useNutritionMealRecordStore, { AVAILABLE_MEAL_TYPES } from 'store/nutritionMealRecordStore';
import HighlightCard from 'components/CustomCards/HighlightCard';
import NutritionStackedBarGraph from 'components/Charts/BarGraphs/NutritionStackedBarGraph';
import SkeletonItem, { SkeletonList } from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
import Toast from 'react-native-toast-message';
import CustomToast from 'components/CustomAction/CustomToast';

const NutritionScreen = () => {
    const navigation = useNavigation();

    const [expandMealsList, setExpandMealsList] = useState(false);

    const [isLoadingRecipes, setIsLoadingRecipes] = useState(true);
    const [recipes, setRecipes] = useState([]);

    const {
        isLoadingMealRecords,
        isLoadingMealStore,
        mealRecords,
        todayTotalCalories,
        isLoadingNutritionHighlights,
        nutritionHighlights,
        lastLoggedMealRecord,
        mealRecordsError,
    } = useNutritionMealRecordStore((state) => state);

    const { setIsLoadingMealStore, getMealRecords, getMealGraphRecords, getNutritionHighlights, clearMealRecordsError } = useNutritionMealRecordStore((state) => state);

    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

    useEffect(() => {
        getMealRecords();
        getMealGraphRecords();
        getNutritionHighlights();
    }, []);

    useEffect(() => {
        setIsLoadingRecipes(true);
        (async () => {
            const dinnerRecipesRes = await recipesService.getAllRecipes({});

            if (dinnerRecipesRes.success) {
                setRecipes(dinnerRecipesRes.data);
            }
            setIsLoadingRecipes(false);
        })();
    }, []);

    // if (mealRecordsError) {
    //     return <CustomAlert
    //         title="Error" message={mealRecordsError}
    //         visible={!!mealRecordsError}
    //         buttons={[{ text: "OK", onPress: clearMealRecordsError }]}
    //         onClose={() => clearMealRecordsError()}
    //     />
    // }

    return (
        <>
            <AppLayout illustration={true}>
                <ScrollView showsVerticalScrollIndicator={false} refreshControl={<RefreshControl refreshing={isLoadingMealStore} onRefresh={() => {
                    getMealRecords();
                    getMealGraphRecords();
                    getNutritionHighlights();
                }} colors={[Colors.primaryGreen]} progressViewOffset={24} />}>
                    <TouchableWithoutFeedback>
                        <View style={{ flex: 1, flexDirection: "column", marginBottom: 80, marginHorizontal: 0 }} >
                            <View style={styles.header}>
                                <Text style={styles.headerTitle}>Nutrition</Text>
                                {
                                    isLoadingMealRecords ? (
                                        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginTop: 8 }}>
                                            <SkeletonItem
                                                width="40%"
                                                height={24}
                                                style={{ marginBottom: 8 }}
                                                isLoading={true}
                                            />
                                            <SkeletonItem
                                                width="30%"
                                                height={16}
                                                style={{ marginBottom: 8 }}
                                                isLoading={true}
                                            />
                                        </View>
                                    ) : (
                                        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                                            <Text style={styles.totalCaloriesText}>
                                                {todayTotalCalories} cal
                                            </Text>
                                            <FormattedTime style={styles.lastLogTimeText} timestamp={lastLoggedMealRecord?.createdAt} prefix="Today at" />
                                        </View>
                                    )
                                }
                            </View>

                            <View>
                                <SkeletonList items={2} height={150} gap={24} borderRadius={25} isLoading={isLoadingMealRecords} style={{ marginTop: 32, marginBottom: 4. }}>
                                    <View style={{ marginTop: 32, marginBottom: 4. }}>
                                        <FlatList
                                            data={expandMealsList ? mealRecords : mealRecords.slice(0, 2)}
                                            keyExtractor={(item, index) => index.toString()}
                                            scrollEnabled={false}
                                            contentContainerStyle={{ gap: 24 }}
                                            renderItem={({ item }) => {
                                                return (
                                                    <NutritionReminderCard
                                                        record={item}
                                                    />
                                                );
                                            }}
                                        />
                                    </View>
                                </SkeletonList>
                                <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginVertical: 16 }}>
                                    <TouchableOpacity
                                        activeOpacity={.5}
                                        onPress={() => setExpandMealsList(prev => !prev)}
                                        style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 30, }}
                                        disabled={isLoadingMealRecords}
                                    >
                                        <Text style={[styles.lastLogTimeText, { fontSize: 16, fontFamily: ThemeFonts.Exo_600, color: Colors.primaryPurple, marginRight: 5, }]}>
                                            {expandMealsList ? 'Show Less' : 'Show More'}
                                        </Text>
                                        <Ionicons
                                            name={expandMealsList ? "chevron-up" : "chevron-down"}
                                            size={16}
                                            color={Colors.primaryPurple}
                                        />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        activeOpacity={.5}
                                        onPress={() => { navigation.navigate("Edit Nutrition History"); }}
                                        style={{ flexDirection: 'row', alignItems: 'center', marginRight: 30 }}
                                    >
                                        <Text style={[styles.lastLogTimeText, { fontSize: 16, fontFamily: ThemeFonts.Exo_600, color: Colors.primaryPurple, marginRight: 5, textDecorationLine: "none", borderRadius: 25, borderWidth: 2, borderColor: Colors.primaryPurple, padding: 4, paddingHorizontal: 8 }]}>Edit History</Text>
                                        {/* <Ionicons
                                        name={"chevron-forward"}
                                        size={16}
                                        color={Colors.primaryPurple}
                                    /> */}
                                    </TouchableOpacity>
                                </View>
                                {/* {mealRecords.length !== AVAILABLE_MEAL_TYPES.length && <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                                <CustomButton title="Add a meal"
                                    onPress={() => { navigation.navigate("Add Meal Type"); }}
                                    style={styles.addMealBtn}
                                    fontFamily={ThemeFonts.Exo_600}
                                />
                                <TouchableOpacity
                                    activeOpacity={.5}
                                    onPress={() => { navigation.navigate("Edit Nutrition History"); }}
                                    style={{ flexDirection: 'row', alignItems: 'center', marginRight: 30, marginTop: 10 }}
                                >
                                    <Text style={[styles.lastLogTimeText, { fontSize: 16, fontFamily: ThemeFonts.Exo_600, color: Colors.primaryPurple, marginRight: 5 }]}>Edit History</Text>
                                </TouchableOpacity>
                            </View>} */}
                            </View>

                            {/* Highlights Section */}
                            <View style={{ marginTop: 20, gap: 20 }}>
                                <NutritionStackedBarGraph currentOpenDropdown={currentOpenDropdown} setCurrentOpenDropdown={setCurrentOpenDropdown} />
                                <HighlightCard highlightData={nutritionHighlights} isLoading={isLoadingNutritionHighlights} />
                            </View>
                            <View style={styles.recipesSection}>
                                <Text style={styles.recipeHeaderTitle}>Recipes</Text>
                                <RecipeList heading={"Meals"} data={recipes} isLoading={isLoadingRecipes} />
                            </View>
                            {/* <View style={{ flex: 1 }}> */}

                            {/* </View> */}
                            <CustomToast error={mealRecordsError} clearErrors={clearMealRecordsError} />
                        </View>
                    </TouchableWithoutFeedback>
                </ScrollView>
            </AppLayout >
            {/* <CustomAlert
                title="Error" message={mealRecordsError}
                visible={!!mealRecordsError}
                buttons={[{ text: "OK", onPress: clearMealRecordsError }]}
                onClose={() => clearMealRecordsError()}
            /> */}
        </>
    );
};

export default NutritionScreen;

const styles = StyleSheet.create({
    header: {
        marginHorizontal: 24,
        top: 10
    },
    headerTitle: {
        fontSize: 35,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    totalCaloriesText: {
        fontSize: 25,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_600
    },
    lastLogTimeText: {
        fontSize: 12,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_500,
        textDecorationLine: "underline"
    },
    addMealBtn: {
        width: 'auto',
        marginTop: 16,
        alignSelf: "flex-start",
        paddingHorizontal: 40,
        height: 36
    },
    sectionTitle: {
        fontSize: 20,
        fontFamily: ThemeFonts.Exo_600,
        color: Colors.black,
        marginBottom: 16,
        marginLeft: 24
    },
    highlightsSection: {
        backgroundColor: Colors.lightGreen,
        marginHorizontal: 8,
        marginVertical: 20,
        padding: 22,
        borderRadius: 25,
        paddingTop: 16,
    },
    highlightsHeading: {
        fontSize: 19,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    highlightsText: {
        fontSize: 18,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_400
    },
    addMealText: {
        fontSize: 12,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_500
    },
    recipesSection: {
        top: 1
    },
    recipeHeaderTitle: {
        fontSize: 35,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_800,
        margin: 24,
        marginBottom: 8,
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: 'stretch',
    },
});