import { FlatList, Image, ScrollView, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import AppLayout from "navigations/components/Layouts/AppLayout";
import { recipesService } from 'services/recipesService';
import { ThemeFonts } from 'constants/theme/fonts';
import { Colors } from 'constants/theme/colors';
import { useNavigation, useRoute } from '@react-navigation/native';
import { CustomButton, CustomLoader } from 'components/CustomAction';
import CustomSelectWithLabel from 'components/CustomAction/CustomSelectWithLabel';
import { setIsEnabledAsync } from 'expo-av/build/Audio';
import NutrientCard from '../components/NutrientCard';
import useNutritionMealStore from 'store/nutritionMealsStore';
import CustomImageWithTextInput from '../components/CustomImageWithTextInput';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
import { useAuth } from 'context/AuthContext';
import CustomToast from 'components/CustomAction/CustomToast';

const Measurement_Options = [
    "grams", "milligrams", "oz"
];

const AddMealScreen = () => {
    const route = useRoute();
    const navigator = useNavigation();
    const scrollViewRef = useRef();
    const { state: { user } } = useAuth();

    const editMealData = useNutritionMealStore(state => state.editMealData);
    const deleteMealData = useNutritionMealStore(state => state.deleteMealData);
    const meals = useNutritionMealStore(state => state.meals);

    const [mealData, setMealData] = useState(meals[route.params.id]);

    const [loading, setLoading] = useState(true);
    const [recipe, setRecipe] = useState();
    const [error, setError] = useState();
    const [selectedQuantity, setSelectedQuantity] = useState(mealData?.quantity);
    const [selectedMeasurement, setSelectedMeasurement] = useState(mealData?.measurement);

    const [closeDropdowns, setCloseDropdowns] = useState(false);
    const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

    useEffect(() => {
        setLoading(true);
        (async () => {
            const res = await recipesService.getSingleRecipe({ id: mealData.recipeId });
            if (res.success) {
                setRecipe(res.data);
            }
            else setError(res.error);
            setLoading(false);
        })();
    }, [mealData.recipeId]);

    const scrollToOffset = (y) => {
        scrollViewRef.current?.scrollTo({ y: y, animated: true });
    };

    if (error) {
        return (
            <AppLayout>
                <View
                    style={{
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        padding: 16,
                        gap: 8,
                    }}
                >
                    <Text style={styles.dataFetchError}>{error}</Text>
                    <CustomButton
                        title={"Go Back"}
                        onPress={() => {
                            navigator.goBack();
                        }}
                    />
                </View>
            </AppLayout>
        )
    }

    return (
        <AppLayout>
            <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false} ref={scrollViewRef}>
                <TouchableWithoutFeedback onPress={() => {
                    setCloseDropdowns(prev => !prev);
                    setCurrentOpenDropdown(null);
                }}>
                    <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }} >
                        <View style={styles.header}>
                            <Text style={styles.headerTitle}>{route.params?.meal_name || route.params?.reminder_name}</Text>
                            <SkeletonItem isLoading={loading} width={"35%"} height={32} style={{
                                marginTop: 8
                            }}>
                                <Text style={styles.totalCaloriesText}>{`${(meals.reduce((total, meal, i) => {
                                    return i == route.params.id ? total + 0 : total + meal.calories;
                                }, 0)) + recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories} cal`}</Text>
                            </SkeletonItem>
                        </View>
                        <View style={{ marginHorizontal: 0, marginTop: 16 }}>
                            <CustomImageWithTextInput
                                isLoading={loading}
                                mealName={recipe?.title}
                                subTile={
                                    recipe?.author === user.id ? "Custom meal" : "Recommended"
                                }
                                editable={false}
                                calories={
                                    selectedQuantity ? recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories : 0
                                }
                                mealImage={recipe?.thumbnailUrl}
                            />
                        </View>
                        <View style={{ marginVertical: 32, gap: 32, marginHorizontal: 0 }}>

                            <CustomSelectWithLabel
                                label="Add Measurement"
                                separateLabel="What is the Measure?"
                                options={Measurement_Options.map((value) => {
                                    return {
                                        label: value,
                                        value: value
                                    };
                                })}
                                selectedValue={selectedMeasurement}
                                onValueChange={(value) => setSelectedMeasurement(value)}
                                labelStyle={{
                                    backgroundColor: Colors.primaryGreen,
                                    color: Colors.white,
                                    paddingVertical: 20
                                }}
                                triggerZ={10}
                                listZ={9}
                                scrollToOffset={(value) => scrollToOffset(value - 200)}
                                outsideClick={closeDropdowns}
                                currentOpenDropdown={currentOpenDropdown}
                                dropdownId={2}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                isLoading={loading}
                            />
                            <CustomSelectWithLabel
                                label="Add Quantity"
                                separateLabel="What is the Quantity"
                                options={recipe?.nutritionByQuantity.map((quantity) => {
                                    return {
                                        label: quantity.quantity,
                                        value: quantity.quantity
                                    };
                                }) || []}
                                selectedValue={selectedQuantity}
                                onValueChange={(value) => setSelectedQuantity(value)}
                                labelStyle={{
                                    backgroundColor: Colors.primaryGreen,
                                    color: Colors.white,
                                    paddingVertical: 20
                                }}
                                triggerZ={7}
                                listZ={6}
                                scrollToOffset={(value) => scrollToOffset(value - 200)}
                                outsideClick={closeDropdowns}
                                currentOpenDropdown={currentOpenDropdown}
                                dropdownId={1}
                                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                                isLoading={loading}
                            />
                        </View>
                        {
                            (selectedQuantity && selectedMeasurement) && (
                                <View style={{ gap: 35, marginHorizontal: 0, marginTop: 24 }}>
                                    <View style={styles.nutritionCardContainer}>
                                        <NutrientCard label="Protein" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.protein} measurement={selectedMeasurement} loading={loading} />
                                        <NutrientCard label="Fats" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fats} measurement={selectedMeasurement} loading={loading} />
                                    </View>
                                    <View style={styles.nutritionCardContainer}>
                                        <NutrientCard label="Carbs" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.carbs} measurement={selectedMeasurement} loading={loading} />
                                        <NutrientCard label="Fiber" value={recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fiber} measurement={selectedMeasurement} loading={loading} />
                                    </View>
                                </View>
                            )
                        }
                        <View style={{ marginTop: 168, marginHorizontal: 0, flexDirection: "row", justifyContent: "space-between", gap: 32 }}>
                            <CustomButton title="Delete" onPress={() => {
                                deleteMealData(route.params.id);
                                navigator.goBack();
                            }}
                                style={{
                                    width: 128,
                                    backgroundColor: Colors.white,
                                    borderWidth: 2,
                                    borderColor: Colors.primaryPurple,
                                }}
                                textColor={Colors.primaryPurple}
                            />
                            <CustomButton title={"Save"} onPress={() => {
                                editMealData({
                                    data: {
                                        recipeId: mealData.recipeId,
                                        measurement: selectedMeasurement,
                                        quantity: selectedQuantity,
                                        name: recipe.title,
                                        image: recipe.thumbnailUrl,
                                        calories: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.calories,
                                        protein: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.protein,
                                        fiber: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fiber,
                                        fats: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.fats,
                                        carbs: recipe?.nutritionByQuantity.find(item => item?.quantity == selectedQuantity)?.carbs,
                                    },
                                    index: route.params.id
                                });
                                navigator.goBack();
                            }}
                                disabled={!(selectedMeasurement && selectedQuantity)}

                                style={{
                                    width: 128
                                }}
                            />
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </ScrollView>
        </AppLayout>
    );
};

export default AddMealScreen;

const styles = StyleSheet.create({
    header: {
        marginHorizontal: 24,
    },
    headerTitle: {
        fontSize: 35,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    totalCaloriesText: {
        fontSize: 25,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_600
    },
    recipeImage: {
        height: 225,
        borderRadius: 25
    },
    recipeImgDataContainer: {
        position: "absolute",
        right: 12,
        left: 12,
        bottom: 12,
        backgroundColor: Colors.veryLightGreen,
        padding: 16,
        // paddingVertical: 12,
        borderRadius: 25,
        flexDirection: "row",
        alignItems: "flex-end"
    },
    loaderContainer: {
        flex: 1,
        bottom: 50,
        justifyContent: "center",
        alignItems: 'stretch',
    },
    recipeName: {
        fontSize: 19,
        color: Colors.black,
        textAlign: 'start',
        fontFamily: ThemeFonts.Exo_700
    },
    nutritionCardContainer: {
        flexDirection: "row",
        gap: 24
    }
});