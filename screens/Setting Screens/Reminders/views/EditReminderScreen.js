import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import CustomInputWithLabel from "components/CustomAction/CustomInputWithLabel";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import {
  getKeyForSound,
  NOTIFICATION_SOUNDS,
  notificationSoundMap,
  TIME_SLOTS,
} from "../../../../constants/constants";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import { reminderService } from "services/remindersService";
import CustomTimePickerWithLabel from "components/CustomAction/CustomTimePickerWithLabel";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import CustomToast from "components/CustomAction/CustomToast";

const EditReminderScreen = () => {
  const navigator = useNavigation();
  const route = useRoute();
  const scrollViewRef = useRef();

  const [isFetchingInitialData, setIsFetchingInitialData] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataFetchError, setDataFetchError] = useState(null);

  const [initialData, setInitialData] = useState({
    label: "",
    category: "",
    sound: "",
    time: "",
  });

  const [currentLabel, setCurrentLabel] = useState("");
  const [currentCategory, setCurrentCategory] = useState("");
  const [selectedSound, setSelectedSound] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [editReminderError, setEditReminderError] = useState(null);

  const [showDeleteConfirmPopup, setShowDeleteConfirmPopup] = useState(false);

  const [validationError, setValidationError] = useState(null);

  useEffect(() => {
    setIsFetchingInitialData(true);

    (async () => {
      const res = await reminderService.getSingleReminder(route.params.id);

      if (res.success) {
        setCurrentLabel(res.data.label);
        setCurrentCategory(res.data.categoryString);
        setSelectedSound(
          getKeyForSound(res.data.sound.ios, res.data.sound.android)
        );

        let hour = res.data.userTime.hour;

        if (res.data.userTime.period == "PM" && hour < 12) {
          hour += 12;
        } else if (res.data.userTime.period == "AM" && hour == 12) {
          hour = 0;
        }

        const date = new Date();
        date.setHours(hour);
        date.setMinutes(res.data.userTime.minute);

        setSelectedTime(date);
        setInitialData({
          label: res.data.label,
          category: res.data.categoryString,
          sound: getKeyForSound(res.data.sound.ios, res.data.sound.android),
          time: date,
        });
      } else {
        setDataFetchError(res.error);
      }
      setIsFetchingInitialData(false);
    })();
  }, [route.params.id]);

  const validateData = () => {
    if (currentLabel.toString().length == 0) {
      setValidationError({
        labelError: "Please enter a label",
      });
      return false;
    }

    if (!selectedTime) {
      setValidationError({
        timeError: "Please select a time",
      });
      return false;
    }

    if (!selectedSound) {
      setValidationError({
        soundError: "Please select a sound",
      });
      return false;
    }

    setValidationError(null);

    return true;
  };

  const handleEditReminder = () => {
    if (
      initialData.label == currentLabel &&
      initialData.category == currentCategory &&
      initialData.sound == selectedSound &&
      initialData.time == selectedTime
    ) {
      navigator.goBack();
      setIsLoading(false);
      return;
    }

    if (!validateData()) return;

    setIsLoading(true);
    (async () => {
      const res = await reminderService.updateReminder({
        id: route.params.id,
        label: currentLabel,
        sound: notificationSoundMap[selectedSound],
        timeStamps: selectedTime,
      });
      if (res.success) {
        navigator.goBack();
      } else {
        setEditReminderError(res.error);
      }
      setIsLoading(false);
    })();
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  const deleteReminder = async () => {
    setShowDeleteConfirmPopup(false);
    setIsLoading(true);
    const response = await reminderService.deleteRemainder(route?.params?.id);

    if (!response.success) {
      setEditReminderError(response.error);
    }

    setIsLoading(false);
    navigator.goBack();
  };

  if (isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  if (dataFetchError) {
    return (
      <AppLayout>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            padding: 16,
            gap: 8,
          }}
        >
          <Text style={styles.dataFetchError}>{dataFetchError}</Text>
          <CustomButton
            title={"Go Back"}
            onPress={() => {
              navigator.goBack();
            }}
          />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <TouchableWithoutFeedback
        onPress={() => {
          setCurrentOpenDropdown(null);
        }}
      >
        <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }}>
          <ScrollView
            ref={scrollViewRef}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{}}
          >
            <TouchableWithoutFeedback
              style={styles.container}
              onPress={() => {
                setCurrentOpenDropdown(null);
              }}
            >
              <View
                style={{ paddingBottom: (NOTIFICATION_SOUNDS.length + 1) * 40 }}
              >
                <CustomToast
                  error={editReminderError}
                  clearErrors={() => {
                    setEditReminderError(null);
                  }}
                />
                <View style={styles.header}>
                  <Text style={styles.headerText}>
                    {currentCategory} Reminder
                  </Text>
                  <MaterialIcons name="edit" size={32} color={Colors.black} />
                </View>
                <View style={{ gap: 16, marginTop: 8 }}>
                  <CustomInputWithLabel
                    value={currentLabel}
                    label="Label"
                    onChangeText={(value) => {
                      setCurrentLabel(value);
                    }}
                    placeholder="Add Label Ex: Lunch"
                    backgroundColor={Colors.primaryPurple}
                    setCloseDropdowns={() => {
                      setCurrentOpenDropdown(null);
                    }}
                    error={validationError?.labelError}
                    clearValidationError={() => {
                      setValidationError((prev) => ({
                        ...prev,
                        labelError: "",
                      }));
                    }}
                    loading={isFetchingInitialData}
                  />
                  <View style={{ marginBottom: 8 }}>
                    <CustomTimePickerWithLabel
                      label="Time"
                      selectedTime={selectedTime}
                      onChangeTime={(value) => {
                        setSelectedTime(value);
                        // If value is null (invalid time), set validation error
                        if (value === null) {
                          setValidationError((prev) => ({
                            ...prev,
                            timeError: "Cannot select future time",
                          }));
                        }
                      }}
                      error={validationError?.timeError}
                      onPress={() => setCurrentOpenDropdown(null)}
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          timeError: "",
                        }));
                      }}
                      loading={isFetchingInitialData}
                    />
                  </View>
                  <CustomSelectWithLabel
                    options={NOTIFICATION_SOUNDS.filter(
                      (cat) => cat.value !== "all"
                    )}
                    label="Sound"
                    selectedValue={selectedSound}
                    onValueChange={(value) => setSelectedSound(value)}
                    triggerZ={6}
                    listZ={5}
                    currentOpenDropdown={currentOpenDropdown}
                    scrollToOffset={(value) => scrollToOffset(value)}
                    error={validationError?.soundError}
                    dropdownId={1}
                    setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                    clearValidationError={() => {
                      setValidationError((prev) => ({
                        ...prev,
                        soundError: "",
                      }));
                    }}
                    isLoading={isFetchingInitialData}
                  />
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          <View style={styles.buttonContainer}>
            <CustomButton
              title={"Delete"}
              onPress={() => {
                setShowDeleteConfirmPopup(true);
              }}
              style={{
                width: 96,
                backgroundColor: Colors.white,
                borderWidth: 2,
                borderColor: Colors.primaryPurple,
              }}
              textColor={Colors.primaryPurple}
            />
            <CustomButton
              title={"Save"}
              onPress={handleEditReminder}
              style={{
                width: 96,
              }}
            />
          </View>
          <CustomAlert
            title={"Delete Reminder"}
            message={"Do you want to delete this reminder?"}
            visible={showDeleteConfirmPopup}
            buttons={[
              {
                text: "Cancel",
                onPress: () => {
                  setShowDeleteConfirmPopup(false);
                },
                style: "allowButton",
              },
              { text: "Delete", onPress: deleteReminder, style: "allowButton" },
            ]}
            onClose={() => {
              setShowDeleteConfirmPopup(false);
            }}
          />
        </View>
      </TouchableWithoutFeedback>
    </AppLayout>
  );
};

export default EditReminderScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 20,
    gap: 10,
    flexDirection: "column",
    justifyContent: "space-between",
    paddingBottom: 70,
    backgroundColor: Colors.lightOrange,
  },
  header: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    marginBottom: 20,
    marginHorizontal: 16,
    gap: 5,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    fontFamily: "Exo_700Bold",
    textTransform: "capitalize",
    textAlign: "left",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
    alignItems: "center",
    marginRight: 5,
  },
  dropdown: {
    marginVertical: 5,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  dataFetchError: {
    top: -20,
    fontSize: 16,
    fontFamily: "Exo_500Medium",
    textAlign: "center",
  },
});
