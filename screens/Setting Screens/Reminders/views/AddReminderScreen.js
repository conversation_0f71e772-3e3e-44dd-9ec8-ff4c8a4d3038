import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useRef, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import CustomInputWithLabel from "components/CustomAction/CustomInputWithLabel";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import {
  CATEGORIES,
  NOTIFICATION_SOUNDS,
  notificationSoundMap,
} from "../../../../constants/constants";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import { reminderService } from "services/remindersService";
import CustomTimePickerWithLabel from "components/CustomAction/CustomTimePickerWithLabel";
import CustomToast from "components/CustomAction/CustomToast";

const AddReminderScreen = () => {
  const navigator = useNavigation();
  const scrollViewRef = useRef();
  const route = useRoute();

  const [isLoading, setLoading] = useState(false);

  const [currentLabel, setCurrentLabel] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedSound, setSelectedSound] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [date, setDate] = useState(new Date(1598051730000));

  const [validationError, setValidationError] = useState(null);
  const [requestError, setRequestError] = useState(null);

  const validateData = () => {
    if (currentLabel.toString().length == 0) {
      setValidationError({
        labelError: "Please enter a label",
      });
      return false;
    }

    if (!selectedCategory) {
      setValidationError({
        categoryError: "Please select a category",
      });
      return false;
    }

    if (!selectedTime) {
      setValidationError({
        timeError: "Please select a time",
      });
      return false;
    }

    if (!selectedSound) {
      setValidationError({
        soundError: "Please select a sound",
      });
      return false;
    }

    setValidationError(null);

    return true;
  };

  const handleAddReminder = async () => {
    setCurrentOpenDropdown(null);

    if (!validateData()) return;

    setLoading(true);

    const res = await reminderService.createReminder({
      category: selectedCategory,
      label: currentLabel,
      sound: notificationSoundMap[selectedSound],
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timeStamps: selectedTime,
      redirectURL: "appetec://reminders",
    });

    setLoading(false);

    if (res.success) {
      navigator.goBack();
    } else {
      setRequestError(res.error);
    }
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (isLoading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <TouchableWithoutFeedback
        onPress={() => {
          setCurrentOpenDropdown(null);
        }}
      >
        <View style={{ flex: 1, flexDirection: "column", marginBottom: 80 }}>
          <ScrollView ref={scrollViewRef} showsVerticalScrollIndicator={false}>
            <CustomToast
              error={requestError}
              clearErrors={() => {
                setRequestError(null);
              }}
            />
            <TouchableWithoutFeedback
              onPress={() => {
                setCurrentOpenDropdown(null);
              }}
            >
              <View
                style={[
                  styles.formWrapper,
                  { paddingBottom: (NOTIFICATION_SOUNDS.length + 1) * 40 },
                ]}
              >
                <View style={{ flex: 1 }}>
                  <View style={styles.header}>
                    <Text
                      style={styles.headerText}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {selectedCategory ? selectedCategory : "Add"} Reminder
                    </Text>
                  </View>
                  <View style={{ gap: 24, marginTop: 8 }}>
                    <CustomInputWithLabel
                      value={currentLabel}
                      label="Label"
                      onChangeText={(value) => {
                        setCurrentLabel(value);
                      }}
                      placeholder="Add Label Ex: Lunch"
                      backgroundColor={Colors.primaryPurple}
                      setCloseDropdowns={() => {
                        setCurrentOpenDropdown(null);
                      }}
                      error={validationError?.labelError}
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          labelError: "",
                        }));
                      }}
                    />
                    <CustomSelectWithLabel
                      options={CATEGORIES.filter((cat) => cat.value !== "all")}
                      label="Category"
                      selectedValue={selectedCategory}
                      onValueChange={(value) => setSelectedCategory(value)}
                      triggerZ={10}
                      listZ={9}
                      currentOpenDropdown={currentOpenDropdown}
                      error={validationError?.categoryError}
                      dropdownId={1}
                      setCurrentOpenDropdown={(id) =>
                        setCurrentOpenDropdown(id)
                      }
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          categoryError: "",
                        }));
                      }}
                    />
                    {/* <CustomSelectWithLabel
                                            options={TIME_SLOTS.filter(time => time.value !== 'all')}
                                            label="Time"
                                            selectedValue={selectedTime}
                                            onValueChange={(value) => setSelectedTime(value)}
                                            triggerZ={8}
                                            listZ={7}
                                            currentOpenDropdown={currentOpenDropdown}
                                            error={validationError?.timeError}
                                            dropdownId={2}
                                            setCurrentOpenDropdown={(id)=>setCurrentOpenDropdown(id)}
                                        /> */}
                    <CustomTimePickerWithLabel
                      label={"Time"}
                      selectedTime={selectedTime}
                      onChangeTime={(value) => {
                        setSelectedTime(value);
                        // If value is null (invalid time), set validation error
                        if (value === null) {
                          setValidationError((prev) => ({
                            ...prev,
                            timeError: "Cannot select future time",
                          }));
                        }
                      }}
                      error={validationError?.timeError}
                      onPress={() => setCurrentOpenDropdown(null)}
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          timeError: "",
                        }));
                      }}
                    />
                    {/* <DateTimePicker
                                            testID="dateTimePicker"
                                            value={date}
                                            mode="time"
                                            is24Hour={false}
                                            onChange={() => {

                                            }}
                                            display='spinner'
                                        /> */}
                    <CustomSelectWithLabel
                      options={NOTIFICATION_SOUNDS}
                      label="Sound"
                      selectedValue={selectedSound}
                      onValueChange={(value) => setSelectedSound(value)}
                      triggerZ={6}
                      listZ={5}
                      currentOpenDropdown={currentOpenDropdown}
                      error={validationError?.soundError}
                      scrollToOffset={scrollToOffset}
                      dropdownId={3}
                      setCurrentOpenDropdown={(id) =>
                        setCurrentOpenDropdown(id)
                      }
                      clearValidationError={() => {
                        setValidationError((prev) => ({
                          ...prev,
                          soundError: "",
                        }));
                      }}
                    />
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          <View style={styles.buttonContainer}>
            <CustomButton
              title={"Save"}
              onPress={handleAddReminder}
              style={{
                width: 84,
              }}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </AppLayout>
  );
};

export default AddReminderScreen;

const styles = StyleSheet.create({
  formWrapper: {
    flex: 1,
    marginBottom: 20,
    gap: 10,
    flexDirection: "column",
    justifyContent: "space-between",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    marginHorizontal: 16,
  },
  headerText: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    fontFamily: "Exo_700Bold",
    textTransform: "capitalize",
    textAlign: "left",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 10,
    alignSelf: "flex-end",
    marginRight: 5,
  },
  dropdown: {
    marginVertical: 5,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
});
