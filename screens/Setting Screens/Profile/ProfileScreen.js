import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  ScrollView,
  RefreshControl,
} from "react-native";
import {
  CustomButton,
  CustomInput,
  CustomLoader,
  CustomSelect,
  CustomAlert,
} from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import { dietOptions } from "constants/options";
import userService from "services/userService";
import { screenWidth } from "constants/sizes";
import { useAuth } from "context/AuthContext";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { ThemeFonts } from "constants/theme/fonts";
import AppLayout from "navigations/components/Layouts/AppLayout";

export const ProfileScreen = () => {
  const scrollViewRef = useRef();

  const [loading, setLoading] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const {
    state: { user: userData, isUpdating },
    updateUserProfile,
    updateUserProfileLocal,
  } = useAuth();

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Reset states
      setErrors({});
      setIsEditing(false);

      // Fetch latest user data
      const response = await userService.getCurrentUser();
      console.log("response ===", response);
      if (response.success) {
        await updateUserProfileLocal(response.data);
        setProfile({
          name: response.data.name,
          email: response.data.email,
          age: response.data.age,
          height: response.data.height,
          weight: response.data.weight,
          diet_preference: response.data.diet_preference,
        });
      }
    } catch (error) {
      console.error("Error refreshing profile:", error);
      setErrors((prev) => ({
        ...prev,
        updateError: "Failed to refresh profile data",
      }));
    } finally {
      setRefreshing(false);
    }
  }, []);

  const [profile, setProfile] = useState({
    name: userData.name,
    email: userData.email,
    age: userData.age,
    height: userData.height,
    weight: userData.weight,
    diet_preference: userData.diet_preference,
  });
  const [errors, setErrors] = useState({});

  // Add email validation function if not already present
  const validateEmail = (email) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const handleSave = async () => {
    setErrors({});

    if (
      userData.name == profile.name &&
      userData.email == profile.email &&
      userData.age == profile.age &&
      userData.height == profile.height &&
      userData.weight == profile.weight &&
      userData.diet_preference == profile.diet_preference
    ) {
      setIsEditing(false);
      return;
    }

    if (!profile?.name || profile?.name.length < 3) {
      setErrors({
        name: "Please enter a valid name.",
      });
      return;
    }
    if (!profile?.age) {
      setErrors({
        age: "Please enter your age.",
      });
      return;
    } else if (Number(profile?.age) < 3 || Number(profile?.age) > 130) {
      setErrors({
        age: "Age must be in between 3-130 years.",
      });
      return;
    }

    if (!profile?.height) {
      setErrors({
        height: "Please enter your height.",
      });
      return;
    } else if (Number(profile?.height) < 50 || Number(profile?.height) > 500) {
      setErrors({
        height: "Height must be in between 50-500 cm",
      });
      return;
    }

    if (!profile?.weight) {
      setErrors({
        weight: "Please enter your weight.",
      });
      return;
    } else if (Number(profile?.weight) < 5 || Number(profile?.weight) > 400) {
      setErrors({
        weight: "Weight must be in between 5-400 kg",
      });
      return;
    }

    // Check if email is being updated and is different
    const isEmailChanged =
      userData.email.toLowerCase().trim() !==
      profile.email.toLowerCase().trim();

    // First validate email format if changed
    if (isEmailChanged) {
      if (!validateEmail(profile.email)) {
        setErrors((prev) => ({
          ...prev,
          email: "Please enter a valid email",
        }));
        return;
      }

      // Show alert for email change
      setShowAlert(true);

      // Here's the key change - we need to call the save functionality from wherever
      // the alert confirmation is handled, rather than immediately continuing

      // We'll assume you have a separate function or component that handles the alert confirmation
      // When confirmed, that component should call a function like "proceedWithSave()"
      return;
    }

    // This becomes your "proceedWithSave" function or gets moved into one
    await saveUserData();
  };

  // Separate function to handle the actual saving
  const saveUserData = async () => {
    setLoading(true);
    try {
      const res = await updateUserProfile(profile);

      if (!res.success) {
        setErrors((prev) => ({
          ...prev,
          updateError: res?.error || "Error updating profile.",
        }));
      } else {
        setIsEditing(false);
      }
    } catch (error) {
      console.error("Error updating user profile:", error);
      setErrors((prev) => ({
        ...prev,
        updateError: "Failed to update profile",
      }));
    } finally {
      setLoading(false);
    }
  };

  const toggleEdit = () => {
    if (isEditing) {
      handleSave();
      scrollToOffset(0);
    } else {
      setIsEditing(true);
    }
  };

  const scrollToOffset = (y) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  if (loading) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <CustomLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout illustration={false}>
      <CustomAlert
        visible={showAlert}
        title="Email Update Verification Required"
        message={`Verification email has been sent to ${profile.email}. \nYour current email will remain valid until you verify the new one.`}
        buttons={[
          {
            text: "OK",
            onPress: () => {
              saveUserData();
              setShowAlert(false);
              setIsEditing(false);
            },
            style: "allowButton",
          },
        ]}
        onClose={() => setShowAlert(false)}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        ref={scrollViewRef}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.primary}
          />
        }
      >
        <TouchableWithoutFeedback
          style={styles.content}
          onPress={() => {
            setCurrentOpenDropdown(null);
          }}
        >
          <View style={[{ flex: 1, justifyContent: "space-between" }]}>
            <View style={[{ flex: 1 }, isEditing && { paddingBottom: 100 }]}>
              {!isEditing ? (
                <Text style={styles.menuHeader}>Profile</Text>
              ) : (
                <View style={styles.editHeader}>
                  <Text style={[styles.menuHeader, { marginRight: 5 }]}>
                    Edit Profile
                  </Text>
                  <MaterialIcons name="edit" size={32} color={Colors.black} />
                </View>
              )}
              {errors.updateError && (
                <Text style={styles.error}>
                  {String(errors.updateError).toUpperCase()}
                </Text>
              )}
              <Text style={styles.label}>name</Text>
              <CustomInput
                value={profile.name}
                onChangeText={(value) =>
                  setProfile({ ...profile, name: value })
                }
                placeholder="Name"
                editable={isEditing}
                setCloseDropdowns={() => {
                  setCurrentOpenDropdown(null);
                }}
                changeBG={true}
                clearValidationError={() => {
                  setErrors((prev) => ({ ...prev, name: "" }));
                }}
              />
              {errors.name && (
                <Text style={styles.error}>
                  {String(errors.name)[0].toUpperCase() +
                    String(errors.name).substring(1)}
                </Text>
              )}
              <Text style={styles.label}>Email</Text>
              <CustomInput
                value={profile.email}
                onChangeText={(value) =>
                  setProfile({ ...profile, email: value })
                }
                placeholder="Email"
                editable={isEditing}
                setCloseDropdowns={() => {
                  setCurrentOpenDropdown(null);
                }}
                changeBG={true}
                clearValidationError={() => {
                  setErrors((prev) => ({ ...prev, email: "" }));
                }}
              />
              {errors.email && (
                <Text style={styles.error}>
                  {String(errors.email)[0].toUpperCase() +
                    String(errors.email).substring(1)}
                </Text>
              )}
              <Text style={styles.label}>Age</Text>
              <CustomInput
                value={`${profile.age}`}
                onChangeText={(value) =>
                  setProfile({ ...profile, age: value ? Number(value) : "" })
                }
                placeholder="Age"
                keyboardType="number-pad"
                editable={isEditing}
                setCloseDropdowns={() => {
                  setCurrentOpenDropdown(null);
                }}
                changeBG={true}
                integer={true}
                isNegative={false}
                clearValidationError={() => {
                  setErrors((prev) => ({ ...prev, age: "" }));
                }}
              />
              {errors.age && (
                <Text style={styles.error}>
                  {String(errors.age)[0].toUpperCase() +
                    String(errors.age).substring(1)}
                </Text>
              )}
              <Text style={styles.label}>Height</Text>
              <CustomInput
                value={
                  isEditing ? profile.height.toString() : `${profile.height} cm`
                }
                onChangeText={(value) =>
                  setProfile({ ...profile, height: value ? Number(value) : "" })
                }
                placeholder="Height (cm)"
                keyboardType="number-pad"
                editable={isEditing}
                setCloseDropdowns={() => {
                  setCurrentOpenDropdown(null);
                }}
                changeBG={true}
                integer={true}
                isNegative={false}
                clearValidationError={() => {
                  setErrors((prev) => ({ ...prev, height: "" }));
                }}
              />
              {errors.height && (
                <Text style={styles.error}>
                  {String(errors.height)[0].toUpperCase() +
                    String(errors.height).substring(1)}
                </Text>
              )}
              <Text style={styles.label}>Weight</Text>

              <CustomInput
                value={
                  isEditing ? profile.weight.toString() : `${profile.weight} kg`
                }
                onChangeText={(value) =>
                  setProfile({ ...profile, weight: value ? value : "" })
                }
                placeholder="Weight (kg)"
                keyboardType="number-pad"
                editable={isEditing}
                setCloseDropdowns={() => {
                  setCurrentOpenDropdown(null);
                }}
                changeBG={true}
                precision={4}
                isNegative={false}
                clearValidationError={() => {
                  setErrors((prev) => ({ ...prev, weight: "" }));
                }}
              />
              {errors.weight && (
                <Text style={styles.error}>
                  {String(errors.weight)[0].toUpperCase() +
                    String(errors.weight).substring(1)}
                </Text>
              )}

              <Text style={styles.label}>Food Preference</Text>
              <View style={{ marginVertical: 10 }}>
                <CustomSelect
                  options={dietOptions}
                  selectedValue={profile.diet_preference}
                  onValueChange={(value) =>
                    setProfile({ ...profile, diet_preference: value })
                  }
                  label="Diet Preference"
                  disabled={!isEditing}
                  currentOpenDropdown={currentOpenDropdown}
                  changeBG={true}
                  scrollToOffset={scrollToOffset}
                  dropdownId={1}
                  setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                />
              </View>
              {errors.diet_preference && (
                <Text style={styles.error}>{errors.diet_preference}</Text>
              )}
            </View>

            <View
              style={[
                styles.buttonContainer,
                !isEditing
                  ? { flexDirection: "row", justifyContent: "flex-end" }
                  : {},
              ]}
            >
              {isEditing && (
                <CustomButton
                  title="Cancel"
                  onPress={() => {
                    setProfile({
                      name: userData.name,
                      email: userData.email,
                      age: userData.age,
                      height: userData.height,
                      weight: userData.weight,
                      diet_preference: userData.diet_preference,
                    });
                    setIsEditing(!isEditing);
                    scrollToOffset(0);
                    setErrors({});
                  }}
                  style={{
                    width: 120,
                  }}
                />
              )}
              <CustomButton
                title={isEditing ? "Save Profile" : "Edit Profile"}
                onPress={toggleEdit}
                style={{
                  width: 120,
                }}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  content: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    padding: 20,
    zIndex: 100,
  },
  menuHeader: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
  },
  label: {
    color: Colors.darkGray,
    fontSize: 12,
    textTransform: "capitalize",
    fontFamily: ThemeFonts.Exo_500,
    marginHorizontal: 24,
    top: 6,
    fontStyle: "italic",
  },
  error: {
    color: "red",
    fontSize: 12,
    marginBottom: 10,
    marginHorizontal: 24,
    fontFamily: ThemeFonts.Exo_500,
  },
  buttonContainer: {
    marginTop: 20,
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 80,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  editHeader: {
    flexDirection: "row",
  },
});
