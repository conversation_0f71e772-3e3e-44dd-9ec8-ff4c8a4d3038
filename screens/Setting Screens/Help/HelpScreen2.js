import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  FlatList,
  ScrollView,
} from "react-native";
import { CustomAlert, CustomSearch } from "components/CustomAction";
import { Colors } from "constants/theme/colors";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenWidth } from "constants/sizes";
import HelpTopicCard from "./components/HelpTopicCard";
import SearchSuggestions from "./components/SearchSuggestions";
import CategoryDataList from "./components/CategoryDataList";
import FAQ_HelpService from "services/FAQ_HelpService";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import { useNavigation } from "@react-navigation/native";
import CustomToast from "components/CustomAction/CustomToast";

export const HelpScreen2 = () => {
  const scrollViewRef = useRef();
  const navigator = useNavigation();

  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [slideAnim] = useState(new Animated.Value(0));
  const [currentTopicId, setCurrentTopicId] = useState(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);

  const [page, setPage] = useState(1);
  const [helpData, setHelpData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [helpError, setHelpError] = useState(null);

  // Fetch help data
  const getHelpData = async () => {
    if (loading || !hasMore) return;
    setLoading(true);

    const response = await FAQ_HelpService.getAllHelps(page);

    if (response.success) {
      if (response.data.length > 0) {
        setHelpData((prevData) => [...prevData, ...response.data]);
        setPage((prevPage) => prevPage + 1);
      } else {
        setHasMore(false);
      }
    } else {
      setHelpError(response.error);
      setHasMore(false);
    }
    setLoading(false);
  };

  useEffect(() => {
    getHelpData();
  }, []);

  const handleLoadMore = () => {
    if (!loading && hasMore && !selectedCategoryId) {
      getHelpData();
    }
  };

  const handleCategorySelect = (category) => {
    // setSelectedCategoryId(category);
    navigator.navigate("Help Details", { id: category });
  };

  const handleBack = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setSelectedCategoryId(null);
      getHelpData();
    });
  };

  const handleSearch = async (text) => {
    setSearchQuery(text);
  };

  useEffect(() => {
    let isActive = true;
    (async () => {
      setSuggestions([]);
      if (searchQuery.length > 0) {
        const searchData = (
          await FAQ_HelpService.getAllHelps(undefined, searchQuery)
        ).data;

        if (isActive) {
          setSuggestions(searchData);
        }
      }
    })();

    return () => {
      isActive = false;
    };
  }, [searchQuery]);

  const animatedRightToLeftSlide = {
    transform: [
      {
        translateX: slideAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -screenWidth],
        }),
      },
    ],
  };

  return (
    <AppLayout illustration={false}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.container}
        contentContainerStyle={[
          hasMore
            ? {
              paddingBottom: 150,
            }
            : { paddingBottom: 90 },
        ]}
        showsVerticalScrollIndicator={false}
        onScroll={(e) => {
          let paddingToBottom = 10;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            handleLoadMore();
          }
        }}
      >
        <CustomToast
          error={helpError}
          clearErrors={() => {
            setHelpError(null);
          }}
        />
        <Text style={styles.headerText}>Hi! What do you need help with?</Text>
        {/* Search Section */}
        <View style={styles.searchSection}>
          <CustomSearch
            value={searchQuery}
            onChangeText={handleSearch}
            placeholder="Search For Options"
          />
          {suggestions.length > 0 && (
            <View style={styles.suggestionsContainer}>
              {suggestions.map((suggestion, index) => (
                <SearchSuggestions
                  key={index}
                  suggestion={suggestion}
                  setSearchQuery={setSearchQuery}
                  setSuggestions={setSuggestions}
                  handleCategorySelect={handleCategorySelect}
                  isLastChild={index === suggestions.length - 1}
                />
              ))}
            </View>
          )}
        </View>
        {/* Categories and Topics Container */}
        <View style={styles.mainContainer}>
          {/* Categories */}
          <Animated.View
            style={[animatedRightToLeftSlide, styles.topicsContainer]}
          >
            <View style={{ width: screenWidth, paddingHorizontal: 20 }}>
              <FlatList
                data={helpData}
                keyExtractor={(item) => item.id.toString()}
                scrollEnabled={false}
                renderItem={({ item }) => (
                  <HelpTopicCard
                    category={item}
                    handleCategorySelect={handleCategorySelect}
                  />
                )}
                contentContainerStyle={{ gap: 15 }}
                ListFooterComponent={loading ? <FlatListBottomLoader /> : null}
              />
            </View>
          </Animated.View>
          {/* )} */}
        </View>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    width: screenWidth,
    left: -20,
    backgroundColor: Colors.white,
    // padding: 20,
    borderRadius: 10,
    // overflow: 'hidden'
  },
  headerText: {
    fontSize: 32,
    top: -15,
    color: Colors.black,
    textAlign: "start",
    marginHorizontal: 24,
    marginVertical: 10,
    fontFamily: "Exo_700Bold",
    padding: 20,
    paddingBottom: 0,
  },
  searchSection: {
    position: "relative",
    marginBottom: 20,
    marginHorizontal: 20,
  },
  suggestionsContainer: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: Colors.white,
    borderRadius: 10,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: Colors.primaryPurple,
  },
  mainContainer: {
    position: "relative",
  },
  categoriesContainer: {
    marginBottom: 10,
  },
  topicsContainer: {
    // position: 'absolute',
    // top: -25,
    flexDirection: "row",
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    // paddingHorizontal: 20,
  },
});
