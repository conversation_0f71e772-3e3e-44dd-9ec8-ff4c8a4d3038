import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import useNutritionMealRecordStore from 'store/nutritionMealRecordStore';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
/**
 * A reusable component for displaying metrics in a section card
 * @param {Object} props - Component props
 * @param {string} props.value - The value to display
 * @param {string} props.label - The label for the value
 * @param {string} props.suffix - Optional suffix to append to the value (e.g., 'g', 'kcal')
 */
const SectionMetric = ({ value, label, suffix = '' }) => (
  <View style={styles.metric}>
    <Text style={styles.metricValue}>{value} {suffix}</Text>
    <Text style={styles.metricLabel}>{label}</Text>
  </View>
);

/**
 * A reusable card component for displaying multiple metrics with dividers
 * @param {Object} props - Component props
 * @param {Array} props.metrics - Array of metric objects with value, label, and optional suffix
 * @param {Object} props.style - Additional styles for the card container
 */
const SectionMetricCard = ({ metrics, style, isLoading, itemCount = 3 }) => {

  if (isLoading) {
    return (
      <View style={[styles.card, style]}>
        {Array(itemCount).fill(0).map((_, index) => (
          <React.Fragment key={index}>
            {index > 0 && <View style={styles.verticalDivider} />}
            <View style={styles.metric}>
              <SkeletonItem
                width="80%"
                height={26}
                borderRadius={4}
                style={{ marginBottom: 4, marginTop: 4 }}
                isLoading={true}
              />
              <SkeletonItem
                width="60%"
                height={14}
                borderRadius={4}
                isLoading={true}
                style={{ marginTop: 8 }}
              />
            </View>
          </React.Fragment>
        ))}
      </View>
    );
  }

  return (
    <View style={[styles.card, style]}>
      {metrics.map((metric, index) => (
        <React.Fragment key={index}>
          {index > 0 && <View style={styles.verticalDivider} />}
          <SectionMetric
            value={metric.value}
            label={metric.label}
            suffix={metric.suffix || ''}
          />
          {/* <SectionMetric
            value={10000}
            label={metric.label}
            suffix={metric.suffix || ''}
          /> */}
        </React.Fragment>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primaryGreen,
    // height: 78,
    padding: 16,
    borderRadius: 25,
    borderColor: Colors.lightGreen,
    borderWidth: 2,
  },
  metric: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 22,
    fontFamily: ThemeFonts.Lexend_700,
    color: Colors.white,
  },
  metricLabel: {
    fontSize: 14,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.white,
    marginTop: 4,
  },
  verticalDivider: {
    width: 1,
    height: "100%",
    backgroundColor: Colors.white,
    marginHorizontal: 8,
  },
});

export default SectionMetricCard;
