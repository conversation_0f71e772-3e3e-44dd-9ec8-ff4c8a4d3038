import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";

export const activityService = {
    // Create a new manual activity entry
    createActivity: async ({ activityType, durationInMinutes }) => {
        try {
            const res = await apiClient.post("/user-health/activity-manual", {
                activityType,
                durationInMinutes,
            });

            return {
                success: true,
                message: res?.msg || "Activity created successfully",
                data: res || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating activity"
            };
        }
    },

    // Get all activities
    getAllActivities: async () => {
        try {
            const res = await apiClient.get("/user-health/activity-manual");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activities"
            };
        }
    },

    // Get activity history for a specific date
    getActivityHistory: async (date) => {
        try {
            const res = await apiClient.get(`/user-health/activity-manual?date=${date}`);

            return {
                success: true,
                data: res || [],
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity history"
            };
        }
    },
    // Get single activity by ID
    getActivityById: async (id) => {
        try {
            const res = await apiClient.get(`/user-health/single-activity-manual/${id}`);
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity"
            };
        }
    },

    // Update activity (only duration can be updated)
    updateActivity: async ({ id, durationInMinutes }) => {
        try {
            const res = await apiClient.put(`/user-health/edit-activity-manual/${id}`, {
                durationInMinutes,
            });

            return {
                success: true,
                message: res?.msg || "Activity updated successfully",
                data: res || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating activity"
            };
        }
    },

    // Delete activity
    deleteActivity: async (id) => {
        try {
            const res = await apiClient.put(`/user-health/delete-activity-manual/${id}`);

            return {
                success: true,
                message: res?.msg || "Activity deleted successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error deleting activity"
            };
        }
    },

    getTodaysLedger: async () => {
        try {
            const res = await apiClient.get("/user-health/health-ledger");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching ledger"
            };
        }
    },

    getRecentActivity: async () => {
        try {
            const res = await apiClient.get("/user-health/last-log-activity");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching recent activity"
            };
        }
    },

    getHighlights: async () => {
        try {
            const response = await apiClient.get(`/health/highlight`);

            return {
                success: true,
                data: response?.data,
            };
        } catch (error) {
            if (error) return {
                success: true,
                data: []
            }
            return {
                success: false,
                error: error?.message || `Error fetching activity highlights`,
            };
        }
    },

    getRecommendedVideo: async () => {
        try {
            const response = await apiClient.get(`/activity_recommendation`);

            return {
                success: true,
                data: response?.video,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || `Error fetching recommended video`,
            };
        }
    },

    getActivityGraph: async ({ filter }) => {
        try {
            const response = await apiClient.get(`/health-analytics/activity-analytics?period=${filter}`);

            console.log("Acticity graph response:", response);

            let responseData = [];
            let timeRange = null;

            if (filter === "weekly") {
                responseData = response.data.map(item => {
                    return {
                        value: item.burnedCalories,
                        label: getDayName(item.date).substring(0, 1)
                    };
                });

                timeRange = getTimePeriod(response.data[0].date, response.data[response.data.length - 1].date);
            }
            else if (filter === "monthly") {
                responseData = response.data.map(item => {
                    const weekNumber = item.period.match(/week_(\d+)/);
                    return {
                        value: item?.burnedCalories || 0,
                        label: `W${weekNumber[1]}`
                    };
                });
                timeRange = getTimePeriod(response.data[0].startDate, response.data[response.data.length - 1].endDate);
            }
            else if (filter == "half_yearly") {
                responseData = response.data.map(item => {
                    return {
                        value: item?.burnedCalories || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = response.data[0].year !== response.data[response.data.length - 1].year
                    ? `${response.data[0].period.substring(0, 3)} ${response.data[0].year} - ${response.data[response.data.length - 1].period.substring(0, 3)} ${response.data[response.data.length - 1].year}`
                    : `${response.data[0].period.substring(0, 3)} - ${response.data[response.data.length - 1].period.substring(0, 3)} ${response.data[0].year}`;
            }
            else if (filter === "yearly") {
                responseData = response.data.map(item => {
                    return {
                        value: item?.burnedCalories || 0,
                        label: item.period.substring(0, 1)
                    };
                });

                timeRange = response.data[0].year !== response.data[response.data.length - 1].year
                    ? `${response.data[0].period} ${response.data[0].year} - ${response.data[response.data.length - 1].period} ${response.data[response.data.length - 1].year}`
                    : `${response.data[0].period} - ${response.data[response.data.length - 1].period} ${response.data[0].year}`;
            }

            console.log({
                activityData: responseData,
                timeRange: timeRange
            });

            return {
                success: true,
                data: {
                    activityData: responseData,
                    timeRange: timeRange
                }
            };

        } catch (error) {
            console.log("error", error);
            return {
                success: false,
                error: error?.message || "Error fetching activity graph",
            };
        }
    },
};