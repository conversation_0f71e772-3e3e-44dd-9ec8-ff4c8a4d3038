import apiClient from "./axiosInstance";

const videosService = {
    getAllVideos: async ({ page = 1, tag }) => {
        try {
            const response = await apiClient.get(`/video_library?${tag && `tag=${tag}`}&page=${page}`);
            return {
                success: true,
                data: response?.videos
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching videos"
            };
        }
    }
}

export default videosService;