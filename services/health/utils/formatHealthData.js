export const formatHealthData = (record) => {
    if (record.name == "Distance") {
        return {
            name: record.name,
            data: aggregateDistanceByDate(record.data)
        }
    }
    else if (record.name == "HeartRate") {
        return {
            name: record.name,
            data: transformHeartRateData(record.data)
        }
    }
    else if (record.name == "Steps") {
        return {
            name: record.name,
            data: aggregateStepsByDate(record.data)
        }
    }
    else if (record.name == "TotalCaloriesBurned") {
        return {
            name: record.name,
            data: aggregateTotalCaloriesBurnedByDate(record.data)
        }
    }
    else return record
}

function aggregateDistanceByDate(data) {
    const aggregated = data.reduce((acc, entry) => {

        const utcDate = new Date(entry.startTime);
        // Format as 'YYYY-MM-DD'
        const date = utcDate.toISOString().split('T')[0];

        if (acc[date] === undefined) {
            acc[date] = 0;
        }

        acc[date] = Math.ceil(acc[date] + entry.distance.inInches);

        return acc;
    }, {});

    return Object.keys(aggregated).map(time => ({
        distance: aggregated[time],
        time: time,
        measurement: "inches"
    }));
}

function transformHeartRateData(data) {
    const getHourKey = (timestamp) => {
        const date = new Date(timestamp);
        return date.getFullYear() + '-' +
            (date.getMonth() + 1) + '-' +
            date.getDate() + '-' +
            date.getHours();
    };

    const getTimeValue = (key) => {
        const [year, month, day, hour] = key.split('-').map(Number);
        const date = new Date(year, month - 1, day, hour, 0, 0);
        return date.toISOString();
    };
    const heartRateMap = new Map();

    for (const entry of data) {
        for (const sample of entry.samples) {
            const hourKey = getHourKey(sample.time);
            const currentMax = heartRateMap.get(hourKey)?.max;
            const currentMin = heartRateMap.get(hourKey)?.min;

            heartRateMap.set(hourKey, {
                max: currentMax ? Math.max(currentMax, sample.beatsPerMinute) : sample.beatsPerMinute,
                min: currentMin ? Math.min(currentMin, sample.beatsPerMinute) : sample.beatsPerMinute,
                startTime: hourKey,
            });
        }
    }

    return Array.from(heartRateMap.values()).map(({ min, max, startTime }) => ({
        min: min,
        max: max,
        time: getTimeValue(startTime)
    }));
}

function aggregateStepsByDate(data) {
    const aggregated = data.reduce((acc, entry) => {
        const utcDate = new Date(entry.startTime);

        const year = utcDate.getFullYear();
        const month = String(utcDate.getMonth() + 1).padStart(2, '0');
        const day = String(utcDate.getDate()).padStart(2, '0');

        const date = `${year}-${month}-${day}`;

        const steps = entry.count;

        if (!acc[date]) {
            acc[date] = {
                totalSteps: 0,
            };
        }

        acc[date].totalSteps = Math.ceil(acc[date].totalSteps + steps);

        return acc;
    }, {});

    return Object.keys(aggregated).map(time => ({
        ...aggregated[time],
        time: time
    }));
}

function aggregateTotalCaloriesBurnedByDate(data) {
    const result = {};

    data.forEach(entry => {
        const utcDate = new Date(entry.startTime);

        const year = utcDate.getFullYear();
        const month = String(utcDate.getMonth() + 1).padStart(2, '0');
        const day = String(utcDate.getDate()).padStart(2, '0');

        const date = `${year}-${month}-${day}`;

        if (!result[date]) {
            result[date] = 0;
        }

        result[date] += entry.energy.inKilocalories;
    });

    return Object.keys(result).map(time => ({
        totalCalories: result[time],
        time: time,
        measurement: "calories"
    }));
}


{
    /*
        export const formatHealthData = (record) => {
        if (record.name == "ActiveCaloriesBurned") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "BasalBodyTemperature") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "BasalMetabolicRate") {
            return {
                name: record.name,
                data: aggregateBMRByDate(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "BloodGlucose") {
            return {
                name: record.name,
                data: tranformBloodGlucose(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "BloodPressure") {
            return {
                name: record.name,
                data: tranformBloodPressure(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "BodyFat") {
            return {
                name: record.name,
                data: transformBodyFat(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "BodyTemperature") {
            return {
                name: record.name,
                data: tranformBodyTemperature(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "BoneMass") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "CervicalMucus") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "CyclingPedalingCadence") {
            return {
                name: record.name,
                data: tranformCyclingPedalingCadence(record.data),
                length: record.data.length
            }
    
        }
        else if (record.name == "Distance") {
            return {
                name: record.name,
                data: {
                    raw: record.data.map(entry => {
                        return {
                            distance: entry.distance,
                            startTime: entry.startTime,
                            endTime: entry.endTime,
                            dataOrigin: entry.metadata?.dataOrigin
                        }
                    }),
                    aggregated: aggregateDistanceByDate(record.data)
                },
                length: record.data.length
            }
        }
        else if (record.name == "ElevationGained") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "ExerciseSession") {
            return {
                name: record.name,
                data: transformExerciseSessionData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "FloorsClimbed") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "HeartRate") {
            return {
                name: record.name,
                data: tranformHeartRateData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "Height") {
            return {
                name: record.name,
                data: transformHeightData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "Hydration") {
            return {
                name: record.name,
                data: aggregateHydrationByDate(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "LeanBodyMass") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "MenstruationFlow") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "MenstruationPeriod") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "Nutrition") {
            return {
                name: record.name,
                data: transformNutritionData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "OvulationTest") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "OxygenSaturation") {
            return {
                name: record.name,
                data: tranfromOxygenSaturationData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "Power") {
            return {
                name: record.name,
                data: transfromPowerData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "RespiratoryRate") {
            return {
                name: record.name,
                data: transformRespiratoryRateData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "RestingHeartRate") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "SexualActivity") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "SleepSession") {
            return {
                name: record.name,
                data: transformSleepSessionData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "Speed") {
            return {
                name: record.name,
                data: transformSpeedData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "StepsCadence") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "Steps") {
            return {
                name: record.name,
                data: {
                    raw: record.data.map(entry => {
                        return {
                            count: entry.count,
                            startTime: entry.startTime,
                            endTime: entry.endTime,
                            dataOrigin: entry.metadata?.dataOrigin
                        }
                    }),
                    aggregated: aggregateStepsByDate(record.data)
                },
                length: record.data.length
            }
        }
        else if (record.name == "TotalCaloriesBurned") {
            return {
                name: record.name,
                data: {
                    raw: record.data.map(entry => {
                        return {
                            energy: entry.energy,
                            startTime: entry.startTime,
                            endTime: entry.endTime,
                            dataOrigin: entry.metadata?.dataOrigin
                        }
                    }),
                    aggregated: aggregateTotalCaloriesBurnedByDate(record.data)
                },
                length: record.data.length
            }
        }
        else if (record.name == "Vo2Max") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else if (record.name == "Weight") {
            return {
                name: record.name,
                data: transformWeightData(record.data),
                length: record.data.length
            }
        }
        else if (record.name == "WheelchairPushes") {
            return {
                name: record.name,
                data: record.data,
                length: record.data.length
            }
        }
        else return record
    }
    
    function aggregateBMRByDate(data) {
        const aggregated = {};
    
        data.forEach(entry => {
            const utcDate = new Date(entry.time);
    
            // Get user's timezone offset in minutes and convert it to milliseconds
            const timezoneOffset = utcDate.getTimezoneOffset() * 60000;
    
            // Adjust the date by subtracting the offset (to get local time)
            const localDate = new Date(utcDate.getTime() - timezoneOffset);
    
            // Format as 'YYYY-MM-DD'
            const date = localDate.toISOString().split('T')[0];
    
            if (!aggregated[date]) {
                aggregated[date] = {
                    measurements: {
                        total: {
                            inWatts: 0,
                            inKilocaloriesPerDay: 0
                        },
                        avg: {
                            inWatts: 0,
                            inKilocaloriesPerDay: 0
                        },
                    },
                    count: 0,
                };
            }
    
            aggregated[date].measurements.total.inWatts += entry.basalMetabolicRate.inWatts;
            aggregated[date].measurements.total.inKilocaloriesPerDay += entry.basalMetabolicRate.inKilocaloriesPerDay;
            aggregated[date].count++;
        });
    
        // Calculate averages
        for (const date in aggregated) {
            aggregated[date].measurements.avg.inWatts = aggregated[date].measurements.total.inWatts / aggregated[date].count;
            aggregated[date].measurements.avg.inKilocaloriesPerDay = aggregated[date].measurements.total.inKilocaloriesPerDay / aggregated[date].count;
        }
    
        return Object.keys(aggregated).map(time => ({
            ...aggregated[time],
            time: time
        }));
    }
    
    function tranformBloodGlucose(data) {
        return data.map(entry => {
            return {
                bloodGlucose: entry.level,
                time: entry.time,
                mealType: entry.mealType,
                mealTiming: entry.relationToMeal,
                source: entry.specimenSource,
            }
        })
    }
    
    function tranformBloodPressure(data) {
        return data.map(entry => {
            return {
                systolic: entry.systolic,
                diastolic: entry.diastolic,
                time: entry.time,
                bodyPosition: entry.bodyPosition,
                measurementLocation: entry.measurementLocation,
            }
        })
    }
    
    function transformBodyFat(data) {
        return data.map(entry => {
            return {
                percentage: entry.percentage,
                time: entry.time,
            }
        })
    }
    
    function tranformBodyTemperature(data) {
        return data.map(entry => {
            return {
                temperature: entry.temperature,
                measurementLocation: entry.measurementLocation,
                time: entry.time,
            }
        })
    }
    
    function tranformCyclingPedalingCadence(data) {
        return data.map(entry => {
            return {
                samples: entry.samples,
                startTime: entry.startTime,
                endTime: entry.endTime,
                dataOrigin: entry.metadata?.dataOrigin
            }
        })
    }
    
    function aggregateDistanceByDate(data) {
        const aggregated = data.reduce((acc, entry) => {
            const utcDate = new Date(entry.startTime);
    
            // Get user's timezone offset in minutes and convert it to milliseconds
            const timezoneOffset = utcDate.getTimezoneOffset() * 60000;
    
            // Adjust the date by subtracting the offset (to get local time)
            const localDate = new Date(utcDate.getTime() - timezoneOffset);
    
            // Format as 'YYYY-MM-DD'
            const date = localDate.toISOString().split('T')[0];
    
            if (!acc[date]) {
                acc[date] = {
                    measurements: {
                        inInches: 1604.7474718469334,
                        inMiles: 0.02532751673662005,
                        inFeet: 133.72895598724443,
                        inKilometers: 0.04076058578491211,
                        inMeters: 40.76058578491211
                    }
                };
            }
    
            acc[date].measurements.inInches = Math.ceil(acc[date].measurements.inInches + entry.distance.inInches);
            acc[date].measurements.inMiles = Math.ceil(acc[date].measurements.inMiles + entry.distance.inMiles);
            acc[date].measurements.inFeet = Math.ceil(acc[date].measurements.inFeet + entry.distance.inFeet);
            acc[date].measurements.inKilometers = Math.ceil(acc[date].measurements.inKilometers + entry.distance.inKilometers);
            acc[date].measurements.inMeters = Math.ceil(acc[date].inMeters + entry.distance.inMeters);
    
            return acc;
        }, {});
    
        return Object.keys(aggregated).map(time => ({
            ...aggregated[time],
            time: time
        }));
    }
    
    function transformExerciseSessionData(data) {
        return data.map(entry => {
            return {
                title: entry.title,
                exerciseType: entry.exerciseType,
                startTime: entry.startTime,
                endTime: entry.endTime,
                laps: entry.laps,
                exerciseRoute: entry.exerciseRoute,
                segments: entry.segments,
                dataOrigin: entry.metadata?.dataOrigin
            }
        })
    }
    
    function transformHeightData(data) {
        return data.map(entry => {
            return {
                height: entry.height,
                time: entry.time,
            }
        })
    }
    
    function tranformHeartRateData(data) {
        return data.map(entry => {
            return {
                samples: entry.samples,
                startTime: entry.startTime,
                endTime: entry.endTime,
                dataOrigin: entry.metadata?.dataOrigin
            }
        })
    }
    
    function aggregateHydrationByDate(data) {
        const aggregated = data.reduce((acc, entry) => {
            const utcDate = new Date(entry.startTime);
    
            // Get user's timezone offset in minutes and convert it to milliseconds
            const timezoneOffset = utcDate.getTimezoneOffset() * 60000;
    
            // Adjust the date by subtracting the offset (to get local time)
            const localDate = new Date(utcDate.getTime() - timezoneOffset);
    
            // Format as 'YYYY-MM-DD'
            const date = localDate.toISOString().split('T')[0];
            if (!acc[date]) {
                acc[date] = {
                    measurements: {
                        totalMilliliters: 0,
                        totalLiters: 0,
                        totalFluidOuncesUs: 0,
                    }
                };
            }
    
            acc[date].measurements.totalMilliliters += entry.volume.inMilliliters;
            acc[date].measurements.totalLiters += entry.volume.inLiters;
            acc[date].measurements.totalFluidOuncesUs += entry.volume.inFluidOuncesUs;
    
            return acc;
        }, {});
    
        return Object.keys(aggregated).map(time => ({
            ...aggregated[time],
            time: time
        }));
    }
    
    function transformNutritionData(data) {
        return data.map(entry => {
            return {
                name: entry.name,
                mealType: entry.mealType,
                nutrients: {
                    biotin: entry.biotin,
                    caffeine: entry.caffeine,
                    calcium: entry.calcium,
                    chloride: entry.chloride,
                    cholesterol: entry.cholesterol,
                    chromium: entry.chromium,
                    copper: entry.copper,
                    dietaryFiber: entry.dietaryFiber,
                    energy: entry.energy,
                    energyFromFat: entry.energyFromFat,
                    folate: entry.folate,
                    folicAcid: entry.folicAcid,
                    iodine: entry.iodine,
                    iron: entry.iron,
                    magnesium: entry.magnesium,
                    manganese: entry.manganese,
                    molybdenum: entry.molybdenum,
                    niacin: entry.niacin,
                    pantothenicAcid: entry.pantothenicAcid,
                    phosphorus: entry.phosphorus,
                    polyunsaturatedFat: entry.polyunsaturatedFat,
                    potassium: entry.potassium,
                    protein: entry.protein,
                    riboflavin: entry.riboflavin,
                    saturatedFat: entry.saturatedFat,
                    selenium: entry.selenium,
                    sodium: entry.sodium,
                    sugar: entry.sugar,
                    thiamin: entry.thiamin,
                    totalCarbohydrate: entry.totalCarbohydrate,
                    totalFat: entry.totalFat,
                    transFat: entry.transFat,
                    unsaturatedFat: entry.unsaturatedFat,
                    vitaminA: entry.vitaminA,
                    vitaminB6: entry.vitaminB6,
                    vitaminB12: entry.vitaminB12,
                    vitaminC: entry.vitaminC,
                    vitaminD: entry.vitaminD,
                    vitaminE: entry.vitaminE,
                    vitaminK: entry.vitaminK,
                    zinc: entry.zinc,
                },
                startTime: entry.startTime,
                endTime: entry.endTime,
            }
        })
    }
    
    function tranfromOxygenSaturationData(data) {
        return data.map(entry => {
            return {
                percentage: entry.percentage,
                time: entry.time,
            }
        })
    }
    
    function transformRespiratoryRateData(data) {
        return data.map(entry => {
            return {
                rate: entry.rate,
                time: entry.time,
            }
        })
    }
    
    function transfromPowerData(data) {
        return data.map(entry => {
            return {
                samples: entry.samples,
                startTime: entry.startTime,
                endTime: entry.endTime,
            }
        })
    }
    
    function transformSleepSessionData(data) {
        return data.map(entry => {
            return {
                title: entry.title,
                stages: entry.stages,
                startTime: entry.startTime,
                endTime: entry.endTime,
            }
        })
    }
    
    function aggregateStepsByDate(data) {
        const aggregated = data.reduce((acc, entry) => {
            const utcDate = new Date(entry.startTime);
    
            // Get user's timezone offset in minutes and convert it to milliseconds
            const timezoneOffset = utcDate.getTimezoneOffset() * 60000;
    
            // Adjust the date by subtracting the offset (to get local time)
            const localDate = new Date(utcDate.getTime() - timezoneOffset);
    
            // Format as 'YYYY-MM-DD'
            const date = localDate.toISOString().split('T')[0];
    
            const steps = entry.count;
    
            if (!acc[date]) {
                acc[date] = {
                    totalSteps: 0,
                };
            }
    
            acc[date].totalSteps = Math.ceil(acc[date].totalSteps + steps);
    
            return acc;
        }, {});
    
        return Object.keys(aggregated).map(time => ({
            ...aggregated[time],
            time: time
        }));
    }
    
    function aggregateTotalCaloriesBurnedByDate(data) {
        const result = {};
    
        data.forEach(entry => {
            const date = new Date(entry.startTime).toISOString().split("T")[0];
    
            if (!result[date]) {
                result[date] = {
                    measurements: {
                        totalKilocalories: 0,
                        totalKilojoules: 0,
                        totalJoules: 0,
                        totalCalories: 0
                    }
                };
            }
    
            result[date].measurements.totalKilocalories += entry.energy.inKilocalories;
            result[date].measurements.totalKilojoules += entry.energy.inKilojoules;
            result[date].measurements.totalJoules += entry.energy.inJoules;
            result[date].measurements.totalCalories += entry.energy.inCalories;
        });
    
        return Object.keys(result).map(time => ({
            ...result[time],
            time: time
        }));
    }
    
    function transformSpeedData(data) {
        return data.map(entry => {
            return {
                samples: entry.samples.map(item => ({
                    speed: item.speed.inKilometersPerHour,
                    time: item.time,
                })),
                startTime: entry.startTime,
                endTime: entry.endTime,
                dataOrigin: entry.metadata?.dataOrigin
            }
        })
    }
    
    function transformWeightData(data) {
        return data.map(entry => {
            return {
                weight: entry.weight,
                time: entry.time,
            }
        })
    }
    */
}