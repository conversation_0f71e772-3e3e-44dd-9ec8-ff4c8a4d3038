import { Platform, NativeModules } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import apiClient from '../axiosInstance';
import { formatHealthData } from './utils/formatHealthData';

// Only try to load the health modules on iOS
let AppleHealthKit = null;
let NativeAppleHealthKit = null;
let Permissions = {};

// Make sure we're on iOS before trying to load the modules
if (Platform.OS === 'ios') {
  try {
    // Try to get the native module directly
    NativeAppleHealthKit = NativeModules.AppleHealthKit;

    // Also import the JS wrapper
    AppleHealthKit = require('react-native-health');

    // Get constants from the module
    if (AppleHealthKit && AppleHealthKit.Constants) {
      Permissions = AppleHealthKit.Constants.Permissions || {};
    }
  } catch (error) {
    console.error('Error loading AppleHealthKit modules:', error);
  }
}

/**
 * Check if Apple HealthKit is available on the device
 * @returns {boolean} Whether HealthKit is available
 */
export const isHealthKitAvailable = () => {
  return Platform.OS === 'ios' && AppleHealthKit !== null;
};

/**
 * Read health data from Apple HealthKit
 * @param {number} days - Number of days to fetch data for
 * @param {Array} filterRecord - Array of record types to filter by
 * @param {boolean} callDumpApi - Whether to call the dump API
 * @param {boolean} formatData - Whether to format the data
 * @returns {Promise<Object>} Result of the operation
 */
export const readAppleHealthData = async (days = 7, filterRecord = [], callDumpApi = false, formatData = false) => {
  if (Platform.OS !== 'ios') {
    return { success: false, error: 'Apple HealthKit only available on iOS' };
  }
  console.log("=== Apple HealthKit Module Debug ===");
  console.log("AppleHealthKit (JS wrapper):", AppleHealthKit);
  console.log("AppleHealthKit type:", typeof AppleHealthKit);
  console.log("AppleHealthKit methods:", AppleHealthKit ? Object.keys(AppleHealthKit) : 'N/A');
  console.log("NativeAppleHealthKit (native module):", NativeAppleHealthKit);
  console.log("NativeAppleHealthKit type:", typeof NativeAppleHealthKit);
  console.log("NativeAppleHealthKit methods:", NativeAppleHealthKit ? Object.keys(NativeAppleHealthKit) : 'N/A');
  console.log("===================================");

  if (!AppleHealthKit && !NativeAppleHealthKit) {
    return { success: false, error: 'Apple HealthKit modules not available' };
  }

  try {
    // Set up date range for queries
    const now = new Date();
    const nDaysAgo = new Date();
    nDaysAgo.setDate(nDaysAgo.getDate() - days);
    nDaysAgo.setHours(0, 0, 0, 0);

    const options = {
      startDate: nDaysAgo.toISOString(),
      endDate: now.toISOString(),
    };

    // Define the record types we want to fetch
    const recordTypes = [
      'Steps',
      'Distance',
      'HeartRate',
      'TotalCaloriesBurned'
    ];

    // Filter record types if needed
    const recordsToFetch = filterRecord.length > 0
      ? recordTypes.filter(record => filterRecord.includes(record))
      : recordTypes;

    // Fetch data for each record type
    const result = await Promise.all(recordsToFetch.map(async (recordType) => {
      let data = [];

      switch (recordType) {
        case 'Steps':
          data = await fetchStepsData(options);
          break;
        case 'Distance':
          data = await fetchDistanceData(options);
          break;
        case 'HeartRate':
          data = await fetchHeartRateData(options);
          break;
        case 'TotalCaloriesBurned':
          data = await fetchCaloriesData(options);
          break;
        default:
          data = [];
      }

      return { name: recordType, data };
    }));
    // Send data to backend
    await sendHealthDataToBackend(formatData ? result.map(formatHealthData) : result, callDumpApi);

    return {
      success: true,
      error: null
    };
  } catch (error) {
    console.log("Error fetching Apple Health data:", error);
    return {
      success: false,
      error: error.message || "Unknown error"
    };
  }
};

/**
 * Fetch steps data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Steps data
 */
const fetchStepsData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getStepCount === 'function') {
      console.log('Fetching steps data with JS wrapper');
      AppleHealthKit.getStepCount(options, (err, results) => {
        if (err) {
          console.log('Error getting step count with JS wrapper:', err);
          // Try native module as fallback
          tryNativeSteps();
        } else {
          console.log('Step count from JS wrapper:', results);
          resolve([{
            count: results.value || 0,
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      // Try native module directly
      tryNativeSteps();
    }

    function tryNativeSteps() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getStepCount === 'function') {
        console.log('Fetching steps data with native module');
        NativeAppleHealthKit.getStepCount(options, (err, results) => {
          if (err) {
            console.log('Error getting step count with native module:', err);
            resolve([]);
          } else {
            console.log('Step count from native module:', results);
            resolve([{
              count: results.value || 0,
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch steps data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch distance data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Distance data
 */
const fetchDistanceData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getDistanceWalkingRunning === 'function') {
      console.log('Fetching distance data with JS wrapper');
      AppleHealthKit.getDistanceWalkingRunning(options, (err, results) => {
        if (err) {
          console.log('Error getting distance with JS wrapper:', err);
          tryNativeDistance();
        } else {
          console.log('Distance from JS wrapper:', results);
          const meters = results.value || 0;
          resolve([{
            distance: {
              inMeters: meters,
              inKilometers: meters / 1000,
              inMiles: meters / 1609.34,
              inFeet: meters * 3.28084,
              inInches: meters * 39.3701
            },
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeDistance();
    }

    function tryNativeDistance() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getDistanceWalkingRunning === 'function') {
        console.log('Fetching distance data with native module');
        NativeAppleHealthKit.getDistanceWalkingRunning(options, (err, results) => {
          if (err) {
            console.log('Error getting distance with native module:', err);
            resolve([]);
          } else {
            console.log('Distance from native module:', results);
            const meters = results.value || 0;
            resolve([{
              distance: {
                inMeters: meters,
                inKilometers: meters / 1000,
                inMiles: meters / 1609.34,
                inFeet: meters * 3.28084,
                inInches: meters * 39.3701
              },
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch distance data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch heart rate data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Heart rate data
 */
const fetchHeartRateData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getHeartRateSamples === 'function') {
      console.log('Fetching heart rate data with JS wrapper');
      AppleHealthKit.getHeartRateSamples(options, (err, results) => {
        if (err) {
          console.log('Error getting heart rate with JS wrapper:', err);
          tryNativeHeartRate();
        } else {
          console.log('Heart rate from JS wrapper:', results);
          resolve([{
            samples: (results || []).map(sample => ({
              beatsPerMinute: sample.value,
              time: sample.startDate
            })),
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeHeartRate();
    }

    function tryNativeHeartRate() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getHeartRateSamples === 'function') {
        console.log('Fetching heart rate data with native module');
        NativeAppleHealthKit.getHeartRateSamples(options, (err, results) => {
          if (err) {
            console.log('Error getting heart rate with native module:', err);
            resolve([]);
          } else {
            console.log('Heart rate from native module:', results);
            resolve([{
              samples: (results || []).map(sample => ({
                beatsPerMinute: sample.value,
                time: sample.startDate
              })),
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch heart rate data');
        resolve([]);
      }
    }
  });
};

/**
 * Fetch calories data from Apple HealthKit
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Calories data
 */
const fetchCaloriesData = (options) => {
  return new Promise((resolve) => {
    // Try JS wrapper first
    if (AppleHealthKit && typeof AppleHealthKit.getActiveEnergyBurned === 'function') {
      console.log('Fetching calories data with JS wrapper');
      AppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
        if (err) {
          console.log('Error getting calories with JS wrapper:', err);
          tryNativeCalories();
        } else {
          console.log('Calories from JS wrapper:', results);
          const calories = results.value || 0;
          resolve([{
            energy: {
              inCalories: calories,
              inKilocalories: calories / 1000,
              inJoules: calories * 4.184,
              inKilojoules: calories * 0.004184
            },
            startTime: options.startDate,
            endTime: options.endDate
          }]);
        }
      });
    } else {
      tryNativeCalories();
    }

    function tryNativeCalories() {
      if (NativeAppleHealthKit && typeof NativeAppleHealthKit.getActiveEnergyBurned === 'function') {
        console.log('Fetching calories data with native module');
        NativeAppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
          if (err) {
            console.log('Error getting calories with native module:', err);
            resolve([]);
          } else {
            console.log('Calories from native module:', results);
            const calories = results.value || 0;
            resolve([{
              energy: {
                inCalories: calories,
                inKilocalories: calories / 1000,
                inJoules: calories * 4.184,
                inKilojoules: calories * 0.004184
              },
              startTime: options.startDate,
              endTime: options.endDate
            }]);
          }
        });
      } else {
        console.log('No method available to fetch calories data');
        resolve([]);
      }
    }
  });
};

/**
 * Send health data to the backend using the same API endpoints as Android
 * @param {Array} result - Health data to send
 * @param {boolean} callDumpApi - Whether to call the dump API
 * @returns {Promise<void>}
 */
const sendHealthDataToBackend = async (result, callDumpApi) => {
  try {
    const deviceId = await DeviceInfo.getUniqueId();
    console.log('Apple Health data fetched:', JSON.stringify(result));

    if (callDumpApi) {
      // Using the same API endpoint as Android for system activity
      await apiClient.post("/user-health/system-activity", {
        deviceId: deviceId,
        os: Platform.OS,
        source: "apple",
        dumps: result.filter((item) => {
          if (item.data.length > 0) return item;
        }),
      });
    } else {
      // Using the same API endpoint as Android for health dump
      console.log("Sending iOS health data to backend:", result);
      await apiClient.post("/health-dump", {
        deviceId: deviceId,
        os: Platform.OS,
        source: "apple",
        data: result.filter((item) => {
          if (item.data.length > 0) return item;
        }),
      });
    }
  } catch (error) {
    console.log("Send Apple Health data error:", error);
  }
};
