import apiClient from './axiosInstance';
const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const FAQ_HelpService = {
    getAllFAQs: async ({ page, searchQuery }) => {
        try {
            const res = await apiClient.get(`${apiUrl}/faq?page=${page}&title=${searchQuery}`);
            return {
                success: true,
                data: res.faqs
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching FAQs'
            };
        }
    },

    getFAQ: async (id) => {
        try {
            const res = await apiClient.get(`${apiUrl}/faq/${id}`);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching FAQ'
            };
        }
    },
    getAllHelps: async (page, search = "") => {
        try {
            const res = await apiClient.get(`${apiUrl}/help?search=${search}${page ? "&page=" + page : ""}`);
            // console.log('getHelps data ===', res.helps);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching Helps'
            };
        }
    },

    getHelp: async (id) => {
        try {
            const res = await apiClient.get(`${apiUrl}/help/${id}`);
            return {
                success: true,
                data: res.data
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || 'Error fetching Helps'
            };
        }
    },
};

export default FAQ_HelpService;