import apiClient from './axiosInstance';
const apiUrl = process.env.EXPO_PUBLIC_API_URL;
console.log('apiUrl ===', apiUrl);
const userService = {
    getCurrentUser: async () => {
        try {
            const res = await apiClient.get(`${apiUrl}/profile`);
            console.log('getCurrentUser ===', res);
            return {
                success: true,
                data: res.user
            };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data || 'Error fetching user profile'
            };
        }
    },

    updateUser: async (userData) => {
        try {
            const res = await apiClient.put(`${apiUrl}/profile`, userData);

            return {
                success: true,
                data: res.user
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || 'Error updating user profile'
            };
        }
    },

    completeUserProfile: async (userProfileData) => {
        try {
            console.log('completeUserProfile ===', userProfileData);
            const res = await apiClient.put(`${apiUrl}/profile/setup_profile`, userProfileData);
            console.log('Res complete user profile', res);

            return {
                success: true,
                data: res
            };
        } catch (error) {
            console.error('Complete profile failed:', error.message || 'Unknown error');
            // throw new Error('Complete profile failed. Please try again.');
            return {
                success: false,
                error: error.message
            };
        }
    },
    isProfileCompleted: async () => {
        try {
            const res = await apiClient.get(`${apiUrl}/status`);
            console.log('Res complete user profile', res.user);

            return {
                success: true,
                data: res.user
            };
        } catch (error) {
            console.error('Complete profile failed:', error.message || 'Unknown error');
            // throw new Error('Complete profile failed. Please try again.');
            return {
                success: false,
                error: error.message
            };
        }
    }
};

export default userService;