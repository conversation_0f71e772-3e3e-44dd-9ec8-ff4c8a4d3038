import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { activityService } from "../services/activityService";

const initialState = {
  // Activity creation/saving state
  isSavingActivity: false,
  savingActivityError: null,

  // Recent activity
  isLoadingRecentActivity: false,
  recentActivity: null,
  recentActivityError: null,

  // Ledger state
  isLoadingActivityLedger: false,
  activityLedgerData: null,
  activityLedgerError: null,


  isLoadingRecommendedActivityVideo: false,
  recommendedActivityVideo: null,
  recommendedActivityVideoError: null,

  // All activities state
  isLoadingAllActivities: false,
  allActivities: [],
  totalBurnedCalories: 0,
  allActivitiesError: null,

  // Activity history state
  isLoadingActivityHistory: false,
  activityHistory: [],
  lastLoggedActivity: null,
  activityHistoryError: null,

  isLoadingActivityGraph: false,
  activityGraphData: null,
  activityGraphTimeRange: null,
  activityGraphFilter: 'weekly',
  activityGraphError: null,

  // Activity summary/highlights state
  isLoadingActivityHighlights: false,
  activityHighlights: [],
  activityHighlightsError: null,
};

const useActivityStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Save a new activity
      saveActivity: async ({ activityType, durationInMinutes }) => {
        set((state) => ({
          ...state,
          isSavingActivity: true,
          savingActivityError: null
        }));

        try {
          const res = await activityService.createActivity({
            activityType,
            durationInMinutes,
          });

          if (res.success) {
            set((state) => ({
              ...state,
              lastLoggedActivity: {
                activityType,
                durationInMinutes,
                createdAt: new Date().toISOString(),
              },
            }));

            // Refresh activity data after saving
            await get().getAllActivities();
            await get().getActivityHistory();

            set((state) => ({
              ...state,
              isSavingActivity: false,
            }));

            return {
              success: true,
              message: res.message,
            };
          } else {
            set((state) => ({
              ...state,
              isSavingActivity: false,
              savingActivityError: res.error,
            }));

            return {
              success: false,
              error: res.error,
            };
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isSavingActivity: false,
            savingActivityError: error.message || "Error saving activity",
          }));

          return {
            success: false,
            error: error.message || "Error saving activity",
          };
        }
      },

      // Get all activities
      getAllActivities: async () => {
        set((state) => ({
          ...state,
          isLoadingAllActivities: true,
          allActivitiesError: null
        }));

        try {
          const res = await activityService.getAllActivities();

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingAllActivities: false,
              allActivities: res.data?.activities || [],
              totalBurnedCalories: res.data?.totalBurnedCalories || 0,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingAllActivities: false,
              allActivities: [],
              allActivitiesError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingAllActivities: false,
            allActivities: [],
            allActivitiesError: error.message || "Error fetching all activities",
          }));
        }
      },

      // Get activity history for current date
      getActivityHistory: async (date = null) => {
        set((state) => ({
          ...state,
          isLoadingActivityHistory: true,
          activityHistoryError: null
        }));

        try {
          const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
          const res = await activityService.getActivityHistory(targetDate);

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingActivityHistory: false,
              activityHistory: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingActivityHistory: false,
              activityHistoryError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingActivityHistory: false,
            activityHistoryError: error.message || "Error fetching activity history",
          }));
        }
      },

      // Get activity summary for dashboard
      getActivityHighlights: async () => {
        set((state) => ({
          ...state,
          isLoadingActivityHighlights: true,
          activityHighlightsError: null
        }));

        try {
          const res = await activityService.getHighlights();

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingActivityHighlights: false,
              activityHighlights: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingActivityHighlights: false,
              activityHighlightsError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingActivityHighlights: false,
            activityHighlightsError: error.message || "Error fetching activity summary",
          }));
        }
      },

      // Update activity
      updateActivity: async ({ id, durationInMinutes }) => {
        try {
          const res = await activityService.updateActivity({
            id,
            durationInMinutes,
          });

          if (res.success) {
            // Refresh activity data after updating
            await get().getAllActivities();
            await get().getActivityHistory();

            return {
              success: true,
              message: res.message,
            };
          } else {
            return {
              success: false,
              error: res.error,
            };
          }
        } catch (error) {
          return {
            success: false,
            error: error.message || "Error updating activity",
          };
        }
      },

      // Delete activity
      deleteActivity: async (id) => {
        try {
          const res = await activityService.deleteActivity(id);

          if (res.success) {
            // Refresh activity data after deleting
            await get().getAllActivities();
            await get().getActivityHistory();

            return {
              success: true,
              message: res.message,
            };
          } else {
            return {
              success: false,
              error: res.error,
            };
          }
        } catch (error) {
          return {
            success: false,
            error: error.message || "Error deleting activity",
          };
        }
      },

      // Get today's ledger
      getTodaysLedger: async () => {
        set((state) => ({
          ...state,
          isLoadingActivityLedger: true,
          activityLedgerError: null
        }));

        const res = await activityService.getTodaysLedger();
        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingActivityLedger: false,
            activityLedgerData: res.data,
          }));
        } else {
          set((state) => ({
            ...state,
            isLoadingActivityLedger: false,
            activityLedgerError: res.error,
          }));
        }
      },

      // Get recent activity
      getRecentActivity: async () => {
        set((state) => ({ isLoadingRecentActivity: true }));

        const res = await activityService.getRecentActivity();

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingRecentActivity: false,
            recentActivity: res.data,
          }));
        } else {
          set((state) => ({
            ...state,
            isLoadingRecentActivity: false,
            recentActivityError: res.error,
          }));
        }
      },

      setActivityGraphFilter: (filter) => set((state) => ({ activityGraphFilter: filter })),

      getActivityGraph: async ({ filter }) => {
        set((state) => ({
          ...state,
          isLoadingActivityGraph: true,
          activityGraphError: null
        }));

        const res = await activityService.getActivityGraph({ filter });

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingActivityGraph: false,
            activityGraphData: res.data?.activityData,
            activityGraphTimeRange: res.data?.timeRange,
          }));
        } else {
          set((state) => ({
            ...state,
            isLoadingActivityGraph: false,
            activityGraphError: res.error,
          }));
        }
      },

      getRecommendedActivityVideo: async () => {
        set((state) => ({
          ...state,
          isLoadingRecommendedVideo: true,
          recommendedVideoError: null
        }));

        try {
          const res = await activityService.getRecommendedVideo();

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingRecommendedVideo: false,
              recommendedActivityVideo: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingRecommendedActivityVideo: false,
              recommendedActivityVideoError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingRecommendedActivityVideo: false,
            recommendedActivityVideoError: error.message || "Error fetching recommended video",
          }));
        }
      },

      // Clear all errors
      clearActivityStoreErrors: () => {
        set((state) => ({
          ...state,
          savingActivityError: null,
          allActivitiesError: null,
          activityHistoryError: null,
          activityHighlightsError: null,
          activityGraphError: null,
          recommendedActivityVideoError: null,
          activityLedgerError: null,
          recentActivityError: null,

        }));
      },

      // Reset store to initial state
      resetActivityStore: () => set({ ...initialState }),
    }),
    {
      name: "activity-store",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

export default useActivityStore;
