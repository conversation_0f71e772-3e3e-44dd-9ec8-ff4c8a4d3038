import {
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import useSleepStore from "store/sleepStore";
import { useAuth } from "context/AuthContext";
import { LineChart } from "react-native-charts-wrapper-microcosmworks";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "weekly" },
  { label: "Month", value: "monthly" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "yearly" },
];

const SleepLineGraph = memo(({ currentOpenDropdown, setCurrentOpenDropdown }) => {
  const {
    isLoadingSleepGraphRecords,
    sleepRecordTimeFilter,
    sleepGraphRecords,
    sleepGraphRecordTime,
  } = useSleepStore((state) => state);

  const { getSleepGraphRecords, setSleepRecordTimeFilter } = useSleepStore(
    (state) => state
  );

  const { state } = useAuth();

  const handleSelectFilter = async (value) => {
    if (value == sleepRecordTimeFilter) return;

    const prevFilter = sleepRecordTimeFilter;
    setSleepRecordTimeFilter(value);

    const sleepRecordSuccess = await getSleepGraphRecords(value);

    if (!sleepRecordSuccess) {
      setSleepRecordTimeFilter(prevFilter);
    }
  };

  if (!sleepGraphRecords || sleepGraphRecords.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <View style={styles.graphContainer}>
      {isLoadingSleepGraphRecords && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000,
            borderRadius: 25,
            backgroundColor: "rgba(0,0,0,0.5)",
          }}
          pointerEvents="box-only"
        >
          <CustomLoader small />
        </View>
      )}
      <View style={{ marginHorizontal: 4 }}>
        <CustomSelect
          options={TIME_FILTER_OPTIONS}
          selectedValue={sleepRecordTimeFilter}
          onValueChange={handleSelectFilter}
          currentOpenDropdown={currentOpenDropdown}
          dropdownId={"sleep_line_graph"}
          setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
          triggerZ={10}
          listZ={9}
          width={140}
          triggerStyle={{
            paddingVertical: 3,
            borderColor: Colors.veryLightGreen,
          }}
          backgroundColor={Colors.lightPurple}
          textColor={Colors.white}
          activeOptionBgColor={Colors.white}
          optionsBgColor={Colors.veryLightGreen}
          optionsBorderWidth={0}
          triggerBorderWidth={1}
          optionsBorderRadius={20}
          alignDropdown="flex-start"
          changeBG={true}
        />
      </View>
      <View style={styles.graphheader}>
        <Text style={styles.graphHeading}>Sleep Graph</Text>
        <Text style={styles.graphTimeRange}>
          {sleepGraphRecordTime ? `(${sleepGraphRecordTime})` : ""}
        </Text>
      </View>
      <LineChart
        style={{
          flexGrow: 1,
          height: 180,
        }}
        data={{
          dataSets: [
            {
              values: sleepGraphRecords.map((data) => {
                return {
                  y: data.value == 0 ? 0.05 : data.value,
                  marker: `${data.value}hr`,
                };
              }),
              label: "Recorded sleep",
              config: {
                circleColor: processColor("transparent"),
                circleHoleColor: processColor("transparent"),
                valueTextColor: processColor("transparent"),
                lineWidth: 2,
                color: processColor(Colors.red),
                mode: "HORIZONTAL_BEZIER",
                highlightColor: processColor(Colors.red),
              },
            },
            {
              values: sleepGraphRecords.map((data) => ({
                y:
                  Number(
                    state.user.goals.filter(
                      (goal) => goal.goal_type == "sleep"
                    )[0].selected_goal
                  ) || 8,
                marker: `${Number(
                  state.user.goals.filter(
                    (goal) => goal.goal_type == "sleep"
                  )[0].selected_goal
                ) || 8
                  }hr`,
              })),
              label: "Sleep goal",
              config: {
                circleColor: processColor("transparent"),
                circleHoleColor: processColor("transparent"),
                valueTextColor: processColor("transparent"),
                dashedLine: {
                  lineLength: 12,
                  spaceLength: 10,
                },
                lineWidth: 1.5,
                color: processColor(Colors.lightGray),
                axisDependency: "RIGHT",
                highlightColor: processColor("transparent"),
              },
            },
          ],
        }}
        chartDescription={{ text: "" }}
        legend={{
          enabled: false,
        }}
        animation={{ durationX: 250, durationY: 0, easingX: "EaseInQuad" }}
        marker={{
          enabled: true,
          markerColor: processColor(Colors.primaryGreen),
          textSize: 12,
          textColor: processColor(Colors.white),
          textAlign: "center"
        }}
        extraOffsets={{
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        }}
        xAxis={{
          valueFormatter: sleepGraphRecords.map((item) => item.label),
          granularityEnabled: true,
          granularity: 1,
          drawAxisLine: false,
          drawGridLines: false,
          fontFamily: ThemeFonts.Exo_500,
          textColor: processColor(Colors.black),
          textSize: 12,
          position: "BOTTOM",
          labelRotationAngle: 0,
          labelCount: sleepGraphRecords.length,
          avoidFirstLastClipping: true,
          // labelCountForce:true,
          granularity: 1,
          yOffset: 28,
        }}
        yAxis={{
          left: {
            drawAxisLine: false,
            drawGridLines: false,
            fontFamily: ThemeFonts.Exo_500,
            textColor: processColor(Colors.black),
            textSize: 12,
            position: "OUTSIDE_CHART",
            labelCount: 5,
            avoidFirstLastClipping: true,
            labelCountForce: true,
            valueFormatter: "##0'hr'",
            axisMinimum: 0,
            axisMaximum:
              sleepGraphRecords
                .map((item) => item.value)
                .reduce((a, b) => Math.max(a, b)) < 5
                ? 5
                : sleepGraphRecords
                  .map((item) => item.value)
                  .reduce((a, b) => Math.max(a, b)) + 2,
          },
          right: {
            drawAxisLine: false,
            drawGridLines: false,
            fontFamily: ThemeFonts.Exo_500,
            textColor: processColor(Colors.black),
            textSize: 12,
            position: "OUTSIDE_CHART",
            labelCount: 5,
            avoidFirstLastClipping: true,
            labelCountForce: true,
            valueFormatter: "##0'hr'",
            axisMinimum: 0,
            axisMaximum:
              sleepGraphRecords
                .map((item) => item.value)
                .reduce((a, b) => Math.max(a, b)) < 5
                ? 5
                : sleepGraphRecords
                  .map((item) => item.value)
                  .reduce((a, b) => Math.max(a, b)) + 2,
          },
        }}
        drawGridBackground={false}
        drawBorders={false}
        touchEnabled={true}
        scaleEnabled={false}
        onSelect={() => { }}
      />
    </View>
  );
});

export default SleepLineGraph;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});