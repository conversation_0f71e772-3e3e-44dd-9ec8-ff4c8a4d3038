import {
  Image,
  processColor,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { memo } from "react";
import { CustomLoader, CustomSelect } from "components/CustomAction";
import { ThemeFonts } from "constants/theme/fonts";
import { Colors } from "constants/theme/colors";
import { LineChart } from "react-native-charts-wrapper-microcosmworks";
import useMoodStore from "store/moodStore";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const TIME_FILTER_OPTIONS = [
  { label: "Week", value: "weekly" },
  { label: "Month", value: "monthly" },
  { label: "6 mon", value: "half_yearly" },
  { label: "Year", value: "yearly" },
];

const convertToYAxisValue = (value) => {
  switch (value) {
    case "happy":
      return 0.05;
    case "moderately happy":
      return 1;
    case "irritated":
      return 2;
    case "anxious":
      return 3;
    case "sad":
      return 4;
    default:
      return 0.05;
  }
};

const MoodLineGraph2 = memo(({ currentOpenDropdown, setCurrentOpenDropdown }) => {
  const {
    isLoadingMoodGraphData,
    moodGraphData,
    moodGraphTimeRange,
    moodGraphFilter,
  } = useMoodStore((state) => state);
  const { getMoodGraphData, setMoodGraphFilter } = useMoodStore(
    (state) => state
  );

  const handleSelectFilter = async (value) => {
    if (value == moodGraphFilter) return;

    const prevFilter = moodGraphFilter;

    setMoodGraphFilter(value);

    const getMoodGraphDataSuccess = await getMoodGraphData({ filter: value });

    if (!getMoodGraphDataSuccess) {
      setMoodGraphFilter(prevFilter);
    }
  };

  if (!moodGraphData || moodGraphData.length == 0) return (
    <View style={[styles.graphContainer, { gap: 16 }]}>
      <SkeletonItem height={28} width={"30%"} borderRadius={25} />
      <SkeletonItem height={32} width={"50%"} borderRadius={10} />

      <SkeletonItem height={180} borderRadius={25} colors={[
        'rgba(255, 159, 193, 0.01)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.55)',
        'rgba(255, 159, 193, 0.15)',
        'rgba(255, 159, 193, 0.01)',
      ]} />
    </View>
  );

  return (
    <TouchableWithoutFeedback
      onPress={() => setCurrentOpenDropdown(null)}
      style={{ flex: 1 }}
    >
      <View style={styles.graphContainer}>
        {isLoadingMoodGraphData && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              borderRadius: 25,
              backgroundColor: "rgba(0,0,0,0.5)",
            }}
            pointerEvents="box-only"
          >
            <CustomLoader small />
          </View>
        )}
        <View style={{ marginHorizontal: 4 }}>
          <CustomSelect
            options={TIME_FILTER_OPTIONS}
            selectedValue={moodGraphFilter}
            onValueChange={(value) => handleSelectFilter(value)}
            dropdownId={"mood_graph2"}
            currentOpenDropdown={currentOpenDropdown}
            setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            triggerZ={10}
            listZ={9}
            width={140}
            triggerStyle={{
              paddingVertical: 3,
              borderColor: Colors.veryLightGreen,
            }}
            backgroundColor={Colors.lightPurple}
            textColor={Colors.white}
            activeOptionBgColor={Colors.white}
            optionsBgColor={Colors.veryLightGreen}
            optionsBorderWidth={0}
            triggerBorderWidth={1}
            optionsBorderRadius={20}
            alignDropdown="flex-start"
            changeBG={true}
          />
        </View>
        <View style={styles.graphheader}>
          <Text style={styles.graphHeading}>Mood Graph</Text>
          <Text style={styles.graphTimeRange}>{`(${moodGraphTimeRange || ""
            })`}</Text>
        </View>
        <View
          style={{
            paddingHorizontal: 0,
            justifyContent: "center",
            flexDirection: "row",
            flexWrap: "nowrap",
            marginBottom: 16,
          }}
        >
          <View
            style={{
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: 8,
              marginBottom: 20,
            }}
          >
            <Image
              source={require("assets/icons/moods/angry.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/sad.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/neutral.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/moderate_happy.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/happy.png")}
              style={{ width: 24, height: 24 }}
            />
          </View>
          <View style={{ backgroundColor: Colors.lightPurple, flexGrow: 1 }}>
            <LineChart
              style={{
                flexGrow: 1,
                height: 180,
              }}
              data={{
                dataSets: [
                  {
                    values: moodGraphData.map((data) => {
                      return {
                        y: convertToYAxisValue(data.value),
                        marker: data?.value ? data.value[0].toUpperCase() + data.value.slice(1) : "Happy",
                      };
                    }),
                    label: "Recorded average weight",
                    config: {
                      circleColor: processColor("transparent"),
                      circleHoleColor: processColor("transparent"),
                      valueTextColor: processColor("transparent"),
                      lineWidth: 2,
                      color: processColor(Colors.red),
                      mode: "LINEAR",
                      highlightColor: processColor(Colors.red),
                    },
                  },
                ],
              }}
              chartDescription={{ text: "" }}
              legend={{
                enabled: false,
              }}
              animation={{
                durationX: 250,
                durationY: 0,
                easingX: "EaseInQuad",
              }}
              marker={{
                enabled: true,
                markerColor: processColor(Colors.primaryGreen),
                textSize: 12,
                textColor: processColor(Colors.white),
                textAlign: "center"
              }}
              extraOffsets={{
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
              }}
              xAxis={{
                valueFormatter: moodGraphData.map((item) => item.label),
                granularityEnabled: true,
                granularity: 1,
                drawAxisLine: false,
                drawGridLines: false,
                fontFamily: ThemeFonts.Exo_500,
                textColor: processColor(Colors.black),
                textSize: 12,
                position: "BOTTOM",
                labelRotationAngle: 0,
                labelCount: moodGraphData.length,
                avoidFirstLastClipping: true,
                // labelCountForce:true,
                granularity: 1,
                yOffset: 20,
              }}
              yAxis={{
                left: {
                  enabled: false,
                  axisMinimum: 0,
                  axisMaximum: 4.1,
                },
                right: {
                  enabled: false,
                  axisMinimum: 0,
                  axisMaximum: 4.1,
                },
              }}
              drawGridBackground={false}
              drawBorders={false}
              touchEnabled={true}
              scaleEnabled={false}
              onSelect={() => { }}
            />
          </View>
          <View
            style={{
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: 8,
              marginBottom: 20,
            }}
          >
            <Image
              source={require("assets/icons/moods/angry.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/sad.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/neutral.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/moderate_happy.png")}
              style={{ width: 24, height: 24 }}
            />
            <Image
              source={require("assets/icons/moods/happy.png")}
              style={{ width: 24, height: 24 }}
            />
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
});

export default MoodLineGraph2;

const styles = StyleSheet.create({
  graphContainer: {
    backgroundColor: Colors.lightPurple,
    borderRadius: 30,
    padding: 16,
    paddingHorizontal: 24,
  },
  graphheader: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "baseline",
    gap: 1,
    marginBottom: 10,
    marginTop: 8,
    marginHorizontal: 4,
    flexWrap: "wrap",
  },
  graphHeading: {
    fontSize: 29,
    fontFamily: ThemeFonts.Exo_700,
    color: Colors.white,
  },
  graphTimeRange: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.white,
    fontStyle: "italic",
  },
  xAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  yAxisText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
  legendWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between",
    // backgroundColor:'red',
    marginHorizontal: 12,
    marginTop: 16,
  },
  legendContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  legendColor: {
    width: 10,
    height: 10,
    borderRadius: 25,
    backgroundColor: Colors.primaryPurple,
  },
  legendText: {
    fontSize: 12,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
  },
});
