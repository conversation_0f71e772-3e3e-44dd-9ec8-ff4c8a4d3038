import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Text,
    Animated,
    Easing,
    FlatList,
} from "react-native";
import { Colors } from "constants/theme/colors";
import Ionicons from "@expo/vector-icons/Ionicons";
import { memo } from "react";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const CustomSelectWithFixedHeight = memo(({
    options = [],
    label,
    selectedValue,
    onValueChange,
    disabled,
    disabledBgColor = Colors.white,
    backgroundColor = Colors.lightGray,
    triggerZ,
    listZ,
    currentOpenDropdown,
    scrollToOffset,
    changeBG,
    showSaveIcon = false,
    width = "auto",
    textColor = Colors.black,
    triggerStyle = {},
    dropdownId,
    setCurrentOpenDropdown,
    activeOptionBgColor = Colors.lightPurple,
    optionsBgColor = Colors.white,
    optionsBorderWidth = 2,
    optionsBorderRadius = 25,
    optionsBorderColor = Colors.primaryPurple,
    triggerBorderWidth = 2,
    clearValidationError,
    showDropdownIcon = true,
    alignDropdown = "auto",
    disabledTextColor = Colors.grey,
    remeasureYpos,
    duration = 400,
    loading = false,
    showArrowUpDown = true,
    maxOptionsShown = 4,
}) => {
    const ref = useRef(null);
    const [triggerHeight, setTriggerHeight] = useState(0);
    const [optionsHeight, setOptionsHeight] = useState(-10);
    const [yPos, setYPos] = useState(0);

    const [isVisible, setIsVisible] = useState(false);
    const animatedHeight = useRef(new Animated.Value(triggerHeight)).current;

    const initialSelectedValue = selectedValue || "";
    const selectedLabel = options.find((option) => {
        return option.value === initialSelectedValue;
    })?.label || label;

    useEffect(() => {
        ref.current?.measureInWindow((x, y,) => {
            setYPos(y);
        });
    }, [ref, remeasureYpos]);


    // Prevent toggling dropdown if disabled
    const toggleDropdown = () => {
        if (scrollToOffset) {
            !isVisible && scrollToOffset(yPos);
        }

        if (!disabled) {
            const shouldOpen = !isVisible;
            setIsVisible(shouldOpen);

            if (shouldOpen) setCurrentOpenDropdown(dropdownId);

            Animated.timing(animatedHeight, {
                toValue: shouldOpen
                    ? (maxOptionsShown > options.length ? options.length : maxOptionsShown) * optionsHeight +
                    triggerHeight -
                    optionsBorderWidth * 2
                    : triggerHeight,
                duration: duration,
                easing: Easing.ease,
                useNativeDriver: false,
            }).start();
        }
    };

    useEffect(() => {
        if (currentOpenDropdown !== dropdownId && isVisible) {
            setIsVisible(false);
            Animated.timing(animatedHeight, {
                toValue: triggerHeight,
                duration: duration,
                useNativeDriver: false,
            }).start();
        }
    }, [currentOpenDropdown]);

    useLayoutEffect(() => {
        Animated.timing(animatedHeight, {
            toValue: triggerHeight,
            duration: 0,
            useNativeDriver: false,
        }).start();
    }, [triggerHeight])

    // Handle selection and update parent state
    const handleSelect = (item) => {
        if (onValueChange) onValueChange(item.value);
        setIsVisible(false);
        Animated.timing(animatedHeight, {
            toValue: triggerHeight,
            duration: duration,
            useNativeDriver: false,
        }).start();
    };

    if (loading) {
        return (
            <View style={[styles.container, { alignSelf: alignDropdown }]} ref={ref}>
                <SkeletonItem borderRadius={25} height={44} isLoading={loading} />
            </View>
        );
    }

    return (
        <View style={[styles.container, { alignSelf: alignDropdown }]} ref={ref}>
            <TouchableOpacity
                activeOpacity={1}
                onPress={toggleDropdown}
                onLayout={(event) => {
                    setTriggerHeight(event.nativeEvent.layout.height);
                }}
                style={[
                    styles.selectBox,
                    isVisible && styles.selectBoxActive,
                    disabled && styles.disabledSelectBox,
                    {
                        backgroundColor:
                            !disabled && changeBG ? backgroundColor : disabledBgColor,
                    },
                    triggerZ ? { zIndex: triggerZ } : {},
                    { ...triggerStyle },
                    { borderWidth: triggerBorderWidth },
                ]}
                disabled={disabled}
            >
                <Text
                    style={[
                        styles.selectedValue,
                        { color: textColor },
                        disabled && { color: disabledTextColor },
                    ]}
                >
                    {selectedLabel}
                </Text>
                {showDropdownIcon &&
                    (disabled && showSaveIcon ? (
                        <Ionicons
                            name="checkmark-circle-sharp"
                            size={24}
                            color={Colors.lightGreen}
                        />
                    ) : (
                        showArrowUpDown && <Ionicons
                            name={isVisible ? "chevron-up" : "chevron-down"}
                            size={20}
                            color={disabled ? disabledTextColor : textColor}
                        />
                    ))}
            </TouchableOpacity>

            {/* {isVisible && !disabled && ( */}
            <Animated.View
                style={[
                    styles.dropdown,
                    {
                        display: triggerHeight != 0 ? "flex" : "none",
                    },
                    listZ ? { zIndex: listZ } : {},
                    { height: animatedHeight },
                    selectedLabel == options[0].label
                        ? { backgroundColor: activeOptionBgColor }
                        : {
                            backgroundColor: optionsBgColor,
                        },
                    {
                        borderWidth: optionsBorderWidth,
                        borderRadius: optionsBorderRadius,
                        borderColor: optionsBorderColor,
                    },
                ]}
            >
                {optionsHeight == -10 && (
                    <View
                        style={[styles.yourViewStyle, { opacity: 0, position: "absolute" }]}
                        onLayout={(event) => {
                            const { height } = event.nativeEvent.layout;
                            setOptionsHeight(height);
                        }}
                    >
                        <TouchableOpacity
                            disabled
                            activeOpacity={0.8}
                            style={[styles.option]}
                        >
                            <Text style={[styles.optionText]}>{"item"}</Text>
                        </TouchableOpacity>
                    </View>
                )}
                <FlatList data={options}
                    keyExtractor={(item) => item.value.toString()}
                    scrollEnabled={true}
                    showsVerticalScrollIndicator={true}
                    nestedScrollEnabled={true}
                    renderItem={({ item, index }) => {
                        const isSelected = item.value === initialSelectedValue;
                        const isDisabled = item.disabled;
                        return (
                            <TouchableOpacity
                                activeOpacity={0.8}
                                style={[
                                    styles.option,
                                    index === 0 && {
                                        paddingTop: triggerHeight - optionsBorderWidth * 2 + 10,
                                    },
                                    isSelected && styles.selectedOption,
                                    isDisabled && styles.disabledOption,
                                    {
                                        backgroundColor: isSelected
                                            ? activeOptionBgColor
                                            : optionsBgColor,
                                    },
                                ]}
                                onPress={() => !isDisabled && handleSelect(item)}
                                onPressIn={() => {
                                    if (!clearValidationError) return;
                                    clearValidationError();
                                }}
                                disabled={isDisabled}
                            >
                                <Text
                                    style={[
                                        styles.optionText,
                                        isSelected && styles.selectedOptionText,
                                        isDisabled && styles.disabledOptionText,
                                    ]}
                                >
                                    {item.label}
                                </Text>
                            </TouchableOpacity>
                        );
                    }} />
            </Animated.View>
            {/* )} */}
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        position: "relative",
    },
    selectBox: {
        position: "relative",
        zIndex: 10,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        borderRadius: 25,
        paddingVertical: 7,
        paddingHorizontal: 23,
        backgroundColor: Colors.white,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 16,
    },
    selectBoxActive: {
        borderColor: Colors.primaryPurple,
    },
    disabledSelectBox: {
        backgroundColor: Colors.white,
        borderColor: Colors.primaryPurple,
    },
    selectedValue: {
        fontSize: 18,
        fontFamily: "Exo_400Regular",
        textTransform: "capitalize",
        textAlign: "left",
    },
    disabledText: {
        color: Colors.grey,
    },
    dropdown: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1,
        borderRadius: 25,
        borderWidth: 2,
        borderColor: Colors.primaryPurple,
        zIndex: 1,
        // elevation: 3,
        // shadowColor: Colors.black,
        // shadowOffset: { width: 0, height: 2 },
        // shadowOpacity: 0.2,
        // shadowRadius: 2,
        overflow: "hidden",
    },
    option: {
        padding: 10,
        backgroundColor: Colors.white,
    },
    optionText: {
        fontSize: 16,
        fontFamily: "Exo_400Regular",
        marginLeft: 12,
        textTransform: "capitalize",
    },
    selectedOption: {
        backgroundColor: Colors.lightPurple,
    },
    selectedOptionText: {
        color: Colors.black,
    },
    disabledOption: {
        opacity: 0.5,
        backgroundColor: Colors.lightGray,
    },
    disabledOptionText: {
        color: Colors.darkGray,
    },
});

export default CustomSelectWithFixedHeight;
