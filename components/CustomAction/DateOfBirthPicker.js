import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from 'constants/theme/colors';
import CustomInput from './CustomInput';
import Ionicons from '@expo/vector-icons/Ionicons';

const DateOfBirthPicker = ({
    value,
    placeholder = "Date of Birth",
    onSelectDate,
    error,
    clearValidationError,
    onPress
}) => {
    const [showCalendar, setShowCalendar] = useState(false);
    const [selectedDate, setSelectedDate] = useState(value ? new Date(value) : null);
    const [tempDate, setTempDate] = useState(value ? new Date(value) : new Date());

    // Set date limits
    const today = new Date();
    const maxDate = today;
    const minDate = new Date(today.getFullYear() - 120, today.getMonth(), today.getDate());

    // Update selectedDate when value prop changes
    useEffect(() => {
        if (value) {
            setSelectedDate(new Date(value));
            setTempDate(new Date(value));
        }
    }, [value]);

    // Calculate age from date of birth
    const calculateAge = (birthDate) => {
        if (!birthDate) return null;

        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDifference = today.getMonth() - birthDate.getMonth();

        if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        return age;
    };

    // Format date for display (Date object to DD/MM/YYYY)
    const formatDateForDisplay = (date) => {
        if (!date) return '';

        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };

    // Handle date picker change
    const handleDateChange = (event, date) => {
        // For Android, we can use the default behavior
        if (Platform.OS === 'android') {
            setShowCalendar(false);
            if (event.type === 'set' && date) {
                setSelectedDate(date);
                // Format date for API (YYYY-MM-DD)
                const formattedDate = date.toISOString().split('T')[0];
                const age = calculateAge(date);
                // Call the parent component's callback with the selected date and age
                onSelectDate(formattedDate, age);
            }
        } else {
            // For iOS, just update the tempDate without closing the picker
            if (date) {
                setTempDate(date);
            }
        }
    };

    // Handle confirming date selection (for iOS)
    const handleConfirmDate = () => {
        setSelectedDate(tempDate);
        setShowCalendar(false);

        // Format date for API (YYYY-MM-DD)
        const formattedDate = tempDate.toISOString().split('T')[0];
        const age = calculateAge(tempDate);

        // Call the parent component's callback with the selected date and age
        onSelectDate(formattedDate, age);
    };

    // Handle canceling date selection (for iOS)
    const handleCancelDate = () => {
        // Reset temp date to the current selected date
        setTempDate(selectedDate || new Date());
        setShowCalendar(false);
    };

    return (
        <View>
            <TouchableOpacity activeOpacity={.9} onPress={() => {
                setShowCalendar(true);
                onPress && onPress();
            }}
                onPressIn={() => {
                    if (clearValidationError) clearValidationError();
                }}
            >
                <View pointerEvents="none">
                    <CustomInput
                        value={selectedDate ? formatDateForDisplay(selectedDate) : ''}
                        placeholder={placeholder}
                        editable={false}
                        icon={<Ionicons name="calendar" size={20} color={Colors.primaryGreen} />}
                    />
                </View>
            </TouchableOpacity>

            {error && <Text style={styles.error}>{error}</Text>}

            {/* For Android: use the default DateTimePicker */}
            {showCalendar && Platform.OS === 'android' && (
                <DateTimePicker
                    value={selectedDate || new Date()}
                    mode="date"
                    display="spinner"
                    maximumDate={maxDate}
                    minimumDate={minDate}
                    onChange={handleDateChange}
                    positiveButton={{
                        textColor: Colors.primaryGreen,
                    }}
                />
            )}

            {/* For iOS: use a Modal with DateTimePicker and buttons */}
            {Platform.OS === 'ios' && (
                <Modal
                    visible={showCalendar}
                    transparent={true}
                    animationType="fade"
                >
                    <View style={styles.modalContainer}>
                        <View style={styles.calendarContainer}>
                            <View style={styles.calendarHeader}>
                                <Text style={styles.calendarTitle}>{placeholder}</Text>
                            </View>

                            <DateTimePicker
                                value={tempDate}
                                mode="date"
                                display="spinner"
                                maximumDate={maxDate}
                                minimumDate={minDate}
                                onChange={handleDateChange}
                                style={styles.datePicker}
                            />

                            <View style={styles.buttonContainer}>
                                <TouchableOpacity
                                    style={styles.cancelButton}
                                    onPress={handleCancelDate}
                                >
                                    <Text style={styles.buttonText}>Cancel</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.confirmButton}
                                    onPress={handleConfirmDate}
                                >
                                    <Text style={[styles.buttonText, { color: Colors.white }]}>Confirm</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    calendarContainer: {
        width: '90%',
        backgroundColor: Colors.white,
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    calendarHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    calendarTitle: {
        fontSize: 18,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    datePicker: {
        width: '100%',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    confirmButton: {
        backgroundColor: Colors.primaryGreen,
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    cancelButton: {
        backgroundColor: Colors.lightGray,
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        fontSize: 16,
        fontFamily: 'Exo_500Medium',
        color: Colors.black,
    },
    ageText: {
        marginLeft: 20,
        marginTop: -5,
        marginBottom: 5,
        fontSize: 14,
        fontFamily: 'Exo_400Regular',
        color: Colors.primaryGreen,
    },
    error: {
        color: 'red',
        marginHorizontal: 7,
        fontSize: 12,
        marginBottom: 0,
    },
});

export default DateOfBirthPicker;