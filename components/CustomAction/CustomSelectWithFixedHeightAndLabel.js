import { StyleSheet, Text, View } from "react-native";
import React from "react";
import CustomSelect from "./CustomSelect";
import { Colors } from "constants/theme/colors";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import CustomSelectWithFixedHeight from "./CustomSelectWithFixedHeight";

const CustomSelectWithFixedHeightAndLabel = ({
    options,
    label,
    triggerZ,
    listZ,
    selectedValue,
    onValueChange,
    error,
    scrollToOffset,
    labelStyle = {},
    gap = 10,
    showSaveIcon = false,
    isEditing = true,
    separateLabel,
    dropdownId,
    currentOpenDropdown,
    setCurrentOpenDropdown,
    clearValidationError,
    backgroundColor = Colors.lightGray,
    disabledSelectColor = Colors.primaryPurple,
    disabledTextColor = Colors.white,
    isLoading = false,
    duration = 400,
    showArrowUpDown = true,
    maxOptionsShown = 4,
}) => {
    return (
        <View style={{ gap: gap }}>
            <Text style={[styles.inputLabel, labelStyle]}>
                {separateLabel ? separateLabel : label}
            </Text>
            <SkeletonItem borderRadius={25} height={44} isLoading={isLoading}>
                <CustomSelectWithFixedHeight
                    options={options}
                    label={label}
                    selectedValue={selectedValue}
                    onValueChange={onValueChange}
                    backgroundColor={backgroundColor}
                    triggerZ={triggerZ}
                    listZ={listZ}
                    currentOpenDropdown={currentOpenDropdown}
                    scrollToOffset={scrollToOffset}
                    showSaveIcon={showSaveIcon}
                    disabled={!isEditing}
                    textColor={isEditing ? Colors.black : Colors.white}
                    changeBG={true}
                    dropdownId={dropdownId}
                    setCurrentOpenDropdown={setCurrentOpenDropdown}
                    clearValidationError={clearValidationError}
                    disabledBgColor={disabledSelectColor}
                    disabledTextColor={disabledTextColor}
                    duration={duration}
                    showArrowUpDown={showArrowUpDown}
                    maxOptionsShown={maxOptionsShown}
                />
            </SkeletonItem>
            {error && <Text style={styles.error}>{error}</Text>}
        </View>
    );
};

export default CustomSelectWithFixedHeightAndLabel;

const styles = StyleSheet.create({
    inputLabel: {
        padding: 16,
        paddingHorizontal: 24,
        backgroundColor: Colors.primaryGreen,
        borderRadius: 25,
        color: Colors.white,
        fontSize: 19,
        fontFamily: "Exo_700Bold",
    },
    error: {
        fontFamily: "Exo_500Medium",
        fontSize: 12,
        color: "red",
        marginLeft: 16,
        position: "absolute",
        bottom: -24,
    },
});
