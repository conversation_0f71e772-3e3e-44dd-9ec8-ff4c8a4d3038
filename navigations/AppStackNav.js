import React, { useContext, useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Dashboard from 'screens/Dashboard/Dashboard';
import { View } from 'react-native';
import { ProfileScreen } from 'screens/Setting Screens/Profile/ProfileScreen';
import { RemindersScreen } from 'screens/Setting Screens/Reminders/views/RemindersScreen';
import { DeviceManagementScreen } from 'screens/Setting Screens/Device Management/DeviceManagementScreen';
import { AuthContext } from 'context/AuthContext';
import { CustomLoader, CustomAlert } from 'components/CustomAction';
import { GoalsScreen } from 'screens/Setting Screens/Goals/GoalsScreen';
import { IntegrationScreen } from 'screens/Setting Screens/App Integration/IntegrationScreen';
import { FAQScreen } from 'screens/Setting Screens/FAQ/FAQScreen';
import { HelpScreen } from 'screens/Setting Screens/Help/HelpScreen';
import { ContactScreen } from 'screens/Setting Screens/Contact/ContactScreen';
import NutritionScreen from 'screens/Menu Screens/Nutrition/views/NutritionScreen';
import AddReminderScreen from 'screens/Setting Screens/Reminders/views/AddReminderScreen';
import EditReminderScreen from 'screens/Setting Screens/Reminders/views/EditReminderScreen';
import ActivityScreen from 'screens/Menu Screens/Activity/views/ActivityScreen';
import MoodScreen from 'screens/Menu Screens/Mood/MoodScreen';
import VideoScreen from 'screens/Menu Screens/Video Library/VideoScreen';
import SleepScreen from 'screens/Menu Screens/Sleep/views/SleepScreen';
import DeviceScreen from 'screens/Menu Screens/Device/views/DeviceScreen';
import EditTimerScreen from 'screens/Menu Screens/Device/views/EditTimerScreen';
import RecipeScreen from 'screens/Menu Screens/Recipe/RecipeScreen';
import { useNavigation } from '@react-navigation/native';
import { HelpScreen2 } from 'screens/Setting Screens/Help/HelpScreen2';
import { HelpScreenDetails } from 'screens/Setting Screens/Help/HelpScreenDetails';
import EditNutritionScreen from 'screens/Menu Screens/Nutrition/views/EditNutritionScreen';
import RecordSleep from 'screens/Menu Screens/Sleep/views/RecordSleep';
import AddMealScreen from 'screens/Menu Screens/Nutrition/views/AddMealScreen';
import EditMealScreen from 'screens/Menu Screens/Nutrition/views/EditMealScreen';
import EditActivitiesScreen from 'screens/Menu Screens/Activity/views/EditActivitiesScreen';
import RecipeDetailsScreen from 'screens/Menu Screens/Recipe/RecipeDetailsScreen';
import useHealthPermissionStore from 'store/healthPermissionStore';
import EditNutritionHistory from 'screens/Menu Screens/Nutrition/views/EditNutritionHistory';
import AddMealType from 'screens/Menu Screens/Nutrition/views/AddMealType';
import ChatBot from 'screens/Chatbot/ChatBot';
import WeightScreen from 'screens/Menu Screens/Weight Loss/views/WeightScreen';
import RecordWeight from 'screens/Menu Screens/Weight Loss/views/RecordWeight';
import useNotificationStore from 'store/notificationStore';
import useNutritionMealRecordStore from 'store/nutritionMealRecordStore';
import SummaryScreen from 'screens/Menu Screens/Summary/SummaryScreen';
import AddUserMeal from 'screens/Menu Screens/Nutrition/views/AddUserMeal';
import EditMealIngredientScreen from 'screens/Menu Screens/Nutrition/views/EditMealIngredientScreen';
import useMoodStore from 'store/moodStore';
import useSleepStore from 'store/sleepStore';
import useUserWeightStore from 'store/userWeightStore';
import AddMealIngredientScreen from 'screens/Menu Screens/Nutrition/views/AddMealIngredientScreen';
import EditActivityScreen from 'screens/Menu Screens/Activity/views/EditActivityScreen';
import VideoDetailsScreen from 'screens/Menu Screens/Video Library/VideoDetailsScreen';

const AppStack = createStackNavigator();

const LogoutComponent = () => {
    const navigation = useNavigation();
    const { signOut } = useContext(AuthContext);
    const { setNotificationStatus } = useNotificationStore(state => state);
    const [showAlert, setShowAlert] = useState(false);
    const [isLoggingOut, setIsLoggingOut] = useState(false);

    const { resetHealthPermissionStore } = useHealthPermissionStore(state => state);
    const { clearMealRecords } = useNutritionMealRecordStore(state => state);
    const { resetMoodStore } = useMoodStore(state => state);
    const { resetSleepStore } = useSleepStore(state => state);
    const { resetWeightStore } = useUserWeightStore(state => state);

    // Show the alert when component mounts
    useEffect(() => {
        console.log('LogoutComponent mounted, showing alert');
        setShowAlert(true);
    }, []);

    const handleLogout = async () => {
        try {
            setIsLoggingOut(true);
            console.log('Starting logout process');

            const success = await signOut();
            console.log('Logout API call completed, success:', success);

            // Reset all stores regardless of API success
            setNotificationStatus({ isNotificationActive: false });
            resetHealthPermissionStore();
            clearMealRecords();
            resetMoodStore();
            resetSleepStore();
            resetWeightStore();

            console.log('All stores reset successfully');
        } catch (error) {
            console.error('Error during logout process:', error);
        } finally {
            setIsLoggingOut(false);
        }
    };

    const handleCancel = () => {
        setShowAlert(false);
        navigation.goBack();
    };

    return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            {isLoggingOut && <CustomLoader />}
            <CustomAlert
                visible={showAlert}
                title="Confirm Logout"
                message="Are you sure you want to logout?"
                buttons={[
                    {
                        text: "Cancel",
                        onPress: handleCancel,
                        style: "cancelButton"
                    },
                    {
                        text: "Logout",
                        onPress: () => {
                            setShowAlert(false);
                            handleLogout();
                        },
                        style: "allowButton"
                    }
                ]}
                onClose={handleCancel}
            />
        </View>
    );
};

const DashStackNav = () => {
    return (
        <AppStack.Navigator screenOptions={{ headerShown: false, animation: "slide_from_right" }}>
            <AppStack.Screen name="dashboard" component={Dashboard} />

            {/* Setting Navigation List */}
            <AppStack.Screen name="Profile" component={ProfileScreen} />
            <AppStack.Screen name="Goals" component={GoalsScreen} />
            <AppStack.Screen name="Device Management" component={DeviceManagementScreen} />
            <AppStack.Screen name="Reminders" component={RemindersScreen} />
            <AppStack.Screen name="Add Reminder" component={AddReminderScreen} />
            <AppStack.Screen name="Edit Reminder" component={EditReminderScreen} />
            <AppStack.Screen name="App Integration" component={IntegrationScreen} />
            <AppStack.Screen name="FAQs" component={FAQScreen} />
            <AppStack.Screen name="Help" component={HelpScreen2} />
            <AppStack.Screen name="Help Details" component={HelpScreenDetails} />
            <AppStack.Screen name="Contact Us" component={ContactScreen} />
            <AppStack.Screen name="Logout" component={LogoutComponent} />

            {/* Menu Tab Navigation List */}
            <AppStack.Screen name="Nutrition" component={NutritionScreen} />
            <AppStack.Screen name="Edit Nutrition" component={EditNutritionScreen} />
            <AppStack.Screen name="Edit Nutrition History" component={EditNutritionHistory} />
            <AppStack.Screen name="Add Meal Type" component={AddMealType} />
            <AppStack.Screen name="Add Meal" component={AddMealScreen} />
            <AppStack.Screen name="Add User Meal" component={AddUserMeal} />
            <AppStack.Screen name="Edit Meal" component={EditMealScreen} />
            <AppStack.Screen name="Add Meal Ingredient" component={AddMealIngredientScreen} />
            <AppStack.Screen name="Edit Meal Ingredient" component={EditMealIngredientScreen} />
            <AppStack.Screen name="Activity" component={ActivityScreen} />
            <AppStack.Screen name="Edit Activities" component={EditActivitiesScreen} />
            <AppStack.Screen name="Edit Activity" component={EditActivityScreen} />
            <AppStack.Screen name="Mood" component={MoodScreen} />
            <AppStack.Screen name='Weight Loss Tracking' component={WeightScreen} />
            <AppStack.Screen name='Record_Weight' component={RecordWeight} />
            <AppStack.Screen name="Video Library" component={VideoScreen} />
            <AppStack.Screen name="Sleep" component={SleepScreen} />
            <AppStack.Screen name="Record_Sleep" component={RecordSleep} />
            <AppStack.Screen name="Device" component={DeviceScreen} />
            <AppStack.Screen name="Edit Timer" component={EditTimerScreen} />
            <AppStack.Screen name="Recipes" component={RecipeScreen} />
            <AppStack.Screen name="Recipe Details" component={RecipeDetailsScreen} />
            <AppStack.Screen name="Summary" component={SummaryScreen} />
            <AppStack.Screen name="Chat Support" component={ChatBot} />
            <AppStack.Screen name="Video Details" component={VideoDetailsScreen} options={{
                animation: "none",
                presentation: "transparentModal",
            }} />
        </AppStack.Navigator>
    );
};

export default DashStackNav;